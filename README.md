# Cypher Chat

A secure end-to-end encrypted chat application with PostgreSQL backend.

## Features

- End-to-end encryption for all messages
- User authentication with JWT
- Real-time messaging with WebSockets
- PostgreSQL database for message and user storage
- Docker Compose setup for easy deployment

## Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Go 1.21+ (for local development)

## Getting Started

### Using Docker Compose

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/cypher-chat.git
   cd cypher-chat
   ```

2. Start the application:
   ```bash
   docker-compose up -d
   ```

3. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080
   - PostgreSQL: localhost:5432

### Local Development

#### Backend

1. Install Go dependencies:
   ```bash
   cd backend
   go mod download
   ```

2. Start a PostgreSQL instance:
   ```bash
   docker run -d --name postgres -p 5432:5432 \
     -e POSTGRES_USER=cypherchat \
     -e POSTGRES_PASSWORD=cypherchat_password \
     -e POSTGRES_DB=cypherchat \
     postgres:15-alpine
   ```

3. Initialize the database:
   ```bash
   psql -h localhost -U cypherchat -d cypherchat -f backend/db/init/01_schema.sql
   ```

4. Run the backend:
   ```bash
   cd backend
   go run cmd/main.go
   ```

#### Frontend

1. Install dependencies:
   ```bash
   cd frontend
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

## API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - Register a new user
  - Request: `{ "username": "user", "password": "pass", "public_key": "key" }`
  - Response: `{ "token": "jwt", "expires_at": "time", "user": {...} }`

- `POST /api/auth/login` - Login a user
  - Request: `{ "username": "user", "password": "pass" }`
  - Response: `{ "token": "jwt", "expires_at": "time", "user": {...} }`

- `GET /api/auth/verify` - Verify a token
  - Headers: `Authorization: Bearer <token>`
  - Response: User object

- `POST /api/auth/logout` - Logout a user
  - Headers: `Authorization: Bearer <token>`
  - Response: 204 No Content

### WebSocket

- `/ws` - WebSocket endpoint for real-time messaging
  - Connect with token: `ws://localhost:8080/ws?token=<jwt_token>`

## Database Schema

The application uses PostgreSQL with the following schema:

- `users` - User accounts
- `sessions` - User sessions
- `messages` - Chat messages
- `groups` - Chat groups
- `group_members` - Group membership

## Security

- All passwords are hashed using bcrypt
- All messages are end-to-end encrypted
- JWT tokens are used for authentication
- HTTPS is recommended for production deployment

## License

MIT
