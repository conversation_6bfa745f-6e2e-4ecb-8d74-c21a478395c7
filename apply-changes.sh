#!/bin/bash

# Apply PostgreSQL and <PERSON><PERSON> changes to the Cypher Chat application

set -e

echo "Applying PostgreSQL and Docker changes to Cypher Chat..."

# Create necessary directories
mkdir -p backend/db/init

# Move new files to their destinations
echo "Moving new files..."
mv README.md.new README.md
mv backend/cmd/main.go.new2 backend/cmd/main.go
mv backend/go.mod.new backend/go.mod
mv backend/pkg/server/server.go.new backend/pkg/server/server.go
mv backend/pkg/server/message.go.new backend/pkg/server/message.go

# Create Docker directories if they don't exist
mkdir -p frontend/node_modules

# Set permissions
chmod +x apply-changes.sh

echo "Creating database directory..."
mkdir -p postgres_data

echo "Changes applied successfully!"
echo ""
echo "To start the application with Docker Compose, run:"
echo "docker-compose up -d"
echo ""
echo "To start the backend locally (requires PostgreSQL):"
echo "cd backend && go run cmd/main.go"
echo ""
echo "To start the frontend locally:"
echo "cd frontend && npm run dev"
