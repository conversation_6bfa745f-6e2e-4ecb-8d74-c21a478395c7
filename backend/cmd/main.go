package main

import (
	"context"
	"database/sql"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
	"github.com/v47on/cypher-chat/backend/pkg/api"
	"github.com/v47on/cypher-chat/backend/pkg/auth"
	"github.com/v47on/cypher-chat/backend/pkg/server"
	"github.com/v47on/cypher-chat/backend/pkg/storage"
)

func main() {
	log.Printf("Starting Cypher Chat server...")

	// Check if we should use SQLite for development
	useSQLite := getEnv("USE_SQLITE", "true") == "true"

	var store storage.Store
	var db *sql.DB
	var err error

	if useSQLite {
		log.Printf("Using SQLite for development...")
		// Use SQLite for development
		sqliteStore, err := storage.NewSQLiteStore("data.db")
		if err != nil {
			log.Fatalf("Failed to initialize SQLite store: %v", err)
		}
		store = sqliteStore
		defer sqliteStore.Close()

		// For user store, we'll use a simple in-memory implementation
		// or create a SQLite-based user store
		log.Printf("Note: Using simplified user authentication for SQLite mode")
	} else {
		log.Printf("Using PostgreSQL...")
		// Get database configuration from environment variables
		dbHost := getEnv("DB_HOST", "localhost")
		dbPort := getEnv("DB_PORT", "5432")
		dbUser := getEnv("DB_USER", "cypherchat")
		dbPassword := getEnv("DB_PASSWORD", "cypherchat_password")
		dbName := getEnv("DB_NAME", "cypherchat")

		// Connect to PostgreSQL
		db, err = connectToDatabase(dbHost, dbPort, dbUser, dbPassword, dbName)
		if err != nil {
			log.Fatalf("Failed to connect to database: %v", err)
		}
		defer db.Close()

		// Initialize message store
		store, err = storage.NewPostgresStore(dbHost, dbPort, dbUser, dbPassword, dbName)
		if err != nil {
			log.Fatalf("Failed to initialize message store: %v", err)
		}
	}

	// Initialize user store (simplified for SQLite mode)
	var userStore auth.UserStore
	if useSQLite {
		userStore = auth.NewMemoryUserStore() // We'll create this
	} else {
		userStore = auth.NewPostgresUserStore(db)
	}

	// Generate or load JWT secret
	jwtSecret := getEnv("JWT_SECRET", "")
	if jwtSecret == "" {
		var err error
		jwtSecret, err = auth.GenerateRandomKey(32)
		if err != nil {
			log.Fatalf("Failed to generate JWT secret: %v", err)
		}
		log.Printf("Generated new JWT secret: %s", jwtSecret)
	}

	// Initialize authentication service
	authService := auth.NewAuthService(
		userStore,
		jwtSecret,
		24*time.Hour,   // Token expiration
		7*24*time.Hour, // Refresh token expiration
	)

	// Create chat server
	chatServer := server.NewServer(store)

	// Create API router
	router := api.NewRouter(authService, chatServer)

	// Start server in a goroutine
	serverPort := getEnv("PORT", "8080")
	go func() {
		log.Printf("Starting HTTP server on :%s", serverPort)
		if err := router.Start(":" + serverPort); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Handle graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Close database connection
	if err := db.Close(); err != nil {
		log.Printf("Error closing database connection: %v", err)
	}

	log.Println("Server shutdown complete")
}

// connectToDatabase establishes a connection to the PostgreSQL database
func connectToDatabase(host, port, user, password, dbname string) (*sql.DB, error) {
	connStr := "host=" + host + " port=" + port + " user=" + user + " password=" + password + " dbname=" + dbname + " sslmode=disable"

	// Try to connect with retries
	var db *sql.DB
	var err error

	for i := 0; i < 5; i++ {
		db, err = sql.Open("postgres", connStr)
		if err != nil {
			log.Printf("Failed to open database connection (attempt %d/5): %v", i+1, err)
			time.Sleep(2 * time.Second)
			continue
		}

		// Test the connection
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err = db.PingContext(ctx); err != nil {
			log.Printf("Failed to ping database (attempt %d/5): %v", i+1, err)
			time.Sleep(2 * time.Second)
			continue
		}

		// Connection successful
		log.Printf("Successfully connected to PostgreSQL database")
		return db, nil
	}

	return nil, err
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
