-- Database optimizations for Cypher Chat
-- This file contains additional indexes and optimizations
-- ============================================================================
-- ADDITIONAL INDEXES FOR PERFORMANCE
-- ============================================================================
-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_messages_recipient_timestamp ON messages(recipient_id, TIMESTAMP DESC);

CREATE INDEX IF NOT EXISTS idx_messages_sender_timestamp ON messages(sender_id, TIMESTAMP DESC);

CREATE INDEX IF NOT EXISTS idx_messages_group_timestamp ON messages(group_id, TIMESTAMP DESC);

-- Index for undelivered messages query
CREATE INDEX IF NOT EXISTS idx_messages_recipient_delivered ON messages(recipient_id, delivered)
WHERE delivered = FALSE;

-- Index for read status queries
CREATE INDEX IF NOT EXISTS idx_messages_recipient_read ON messages(recipient_id, READ)
WHERE READ = FALSE;

-- Composite index for conversation queries (between two users)
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(
    LEAST(sender_id, recipient_id),
    GREATEST(sender_id, recipient_id),
    TIMESTAMP DESC
)
WHERE group_id IS NULL;

-- Index for session cleanup (expired sessions)
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions(expires_at)
WHERE is_valid = TRUE;

-- Index for user status queries
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)
WHERE is_active = TRUE;

-- Index for recent activity
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login DESC)
WHERE is_active = TRUE;

-- ============================================================================
-- PARTIAL INDEXES FOR BETTER PERFORMANCE
-- ============================================================================
-- Only index active users
CREATE INDEX IF NOT EXISTS idx_users_username_active ON users(username)
WHERE is_active = TRUE;

-- Only index valid sessions
CREATE INDEX IF NOT EXISTS idx_sessions_token_valid ON sessions(token)
WHERE is_valid = TRUE;

-- Only index active groups
CREATE INDEX IF NOT EXISTS idx_groups_active ON groups(created_at DESC)
WHERE is_active = TRUE;

-- ============================================================================
-- MATERIALIZED VIEWS FOR ANALYTICS (Optional)
-- ============================================================================
-- View for user message counts (can be materialized for better performance)
CREATE
OR REPLACE VIEW user_message_stats AS
SELECT u.id,
    u.username,
    COUNT(m.id) AS total_messages,
    COUNT(
        CASE
            WHEN m.timestamp > NOW() - INTERVAL '24 hours' THEN 1
        END
    ) AS messages_24h,
    COUNT(
        CASE
            WHEN m.timestamp > NOW() - INTERVAL '7 days' THEN 1
        END
    ) AS messages_7d,
    MAX(m.timestamp) AS last_message_at
FROM users u
    LEFT JOIN messages m ON (u.id = m.sender_id)
WHERE u.is_active = TRUE
GROUP BY u.id,
    u.username;

-- ============================================================================
-- FUNCTIONS FOR OPTIMIZED QUERIES
-- ============================================================================
-- Function to get conversation between two users with pagination
CREATE
OR REPLACE FUNCTION get_conversation(
    user1_id UUID,
    user2_id UUID,
    page_limit INTEGER DEFAULT 50,
    page_offset INTEGER DEFAULT 0
) RETURNS TABLE (
    id UUID,
    sender_id UUID,
    recipient_id UUID,
    content BYTEA,
    message_type VARCHAR(20),
    msg_timestamp TIMESTAMP WITH TIME ZONE,
    delivered BOOLEAN,
    is_read BOOLEAN
) AS $$
BEGIN RETURN QUERY
SELECT m.id,
    m.sender_id,
    m.recipient_id,
    m.content,
    m.message_type,
    m.timestamp,
    m.delivered,
    m.read
FROM messages m
WHERE m.group_id IS NULL
    AND (
        (
            m.sender_id = user1_id
            AND m.recipient_id = user2_id
        )
        OR (
            m.sender_id = user2_id
            AND m.recipient_id = user1_id
        )
    )
ORDER BY m.timestamp DESC
LIMIT page_limit OFFSET page_offset;

END;

$$ LANGUAGE plpgsql;

-- Function to mark messages as read efficiently
CREATE
OR REPLACE FUNCTION mark_messages_read(
    p_recipient_id UUID,
    p_sender_id UUID DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE updated_count INTEGER;

BEGIN IF p_sender_id IS NULL THEN -- Mark all unread messages for recipient as read
UPDATE messages
SET READ = TRUE
WHERE recipient_id = p_recipient_id
    AND READ = FALSE;

ELSE -- Mark messages from specific sender as read
UPDATE messages
SET READ = TRUE
WHERE recipient_id = p_recipient_id
    AND sender_id = p_sender_id
    AND READ = FALSE;

END IF;

GET DIAGNOSTICS updated_count = ROW_COUNT;

RETURN updated_count;

END;

$$ LANGUAGE plpgsql;

-- Function to clean up expired sessions
CREATE
OR REPLACE FUNCTION cleanup_expired_sessions() RETURNS INTEGER AS $$
DECLARE deleted_count INTEGER;

BEGIN
DELETE FROM sessions
WHERE expires_at < NOW()
    OR is_valid = FALSE;

GET DIAGNOSTICS deleted_count = ROW_COUNT;

RETURN deleted_count;

END;

$$ LANGUAGE plpgsql;

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC MAINTENANCE
-- ============================================================================
-- Trigger to update last_login when a new session is created
CREATE
OR REPLACE FUNCTION update_user_last_login() RETURNS TRIGGER AS $$
BEGIN
UPDATE users
SET last_login = NEW .created_at
WHERE id = NEW .user_id;

RETURN NEW;

END;

$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_last_login AFTER
INSERT ON sessions FOR EACH ROW EXECUTE FUNCTION update_user_last_login();

-- ============================================================================
-- PERFORMANCE MONITORING VIEWS
-- ============================================================================
-- View to monitor slow queries (requires pg_stat_statements extension)
-- Note: This view is commented out as pg_stat_statements extension is not available by default
-- To enable, install the extension first: CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
/*
 CREATE OR REPLACE VIEW slow_queries AS
 SELECT query,
 calls,
 total_time,
 mean_time,
 rows,
 100.0 * shared_blks_hit / NULLIF(shared_blks_hit + shared_blks_read, 0) AS hit_percent
 FROM pg_stat_statements
 WHERE query LIKE '%messages%'
 OR query LIKE '%users%'
 OR query LIKE '%sessions%'
 ORDER BY mean_time DESC;
 */
-- View to monitor index usage
CREATE
OR REPLACE VIEW index_usage AS
SELECT schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE
        WHEN idx_scan = 0 THEN 'Never used'
        WHEN idx_scan < 100 THEN 'Rarely used'
        ELSE 'Frequently used'
    END AS usage_status
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- ============================================================================
-- MAINTENANCE PROCEDURES
-- ============================================================================
-- Procedure to analyze table statistics
CREATE
OR REPLACE FUNCTION update_table_statistics() RETURNS VOID AS $$
BEGIN ANALYZE users;

ANALYZE sessions;

ANALYZE messages;

ANALYZE groups;

ANALYZE group_members;

END;

$$ LANGUAGE plpgsql;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================
COMMENT ON INDEX idx_messages_recipient_timestamp IS 'Optimizes queries for user message history';

COMMENT ON INDEX idx_messages_conversation IS 'Optimizes conversation queries between two users';

COMMENT ON INDEX idx_messages_recipient_delivered IS 'Optimizes undelivered message queries';

COMMENT ON FUNCTION get_conversation IS 'Efficiently retrieves conversation between two users with pagination';

COMMENT ON FUNCTION mark_messages_read IS 'Bulk update function for marking messages as read';

COMMENT ON FUNCTION cleanup_expired_sessions IS 'Maintenance function to remove expired sessions';