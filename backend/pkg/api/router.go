package api

import (
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/v47on/cypher-chat/backend/pkg/auth"
	"github.com/v47on/cypher-chat/backend/pkg/server"
)

// Router handles HTTP routing
type Router struct {
	router      *mux.Router
	authHandler *AuthHandler
	chatServer  *server.Server
}

// NewRouter creates a new router
func NewRouter(authService *auth.AuthService, chatServer *server.Server) *Router {
	router := mux.NewRouter()
	authHandler := NewAuthHandler(authService)

	r := &Router{
		router:      router,
		authHandler: authHandler,
		chatServer:  chatServer,
	}

	r.setupRoutes()
	return r
}

// setupRoutes configures the routes
func (r *Router) setupRoutes() {
	// Configure CORS first
	r.router.Use(r.corsMiddleware)

	// Public routes
	r.router.HandleFunc("/api/auth/register", r.authHandler.Register).Methods("POST", "OPTIONS")
	r.router.HandleFunc("/api/auth/login", r.authHandler.Login).Methods("POST", "OPTIONS")
	r.router.HandleFunc("/api/auth/verify", r.authHandler.VerifyToken).Methods("GET", "OPTIONS")

	// Health check
	r.router.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"status":"ok"}`))
	}).Methods("GET")

	// Protected routes
	protected := r.router.PathPrefix("/api").Subrouter()
	protected.Use(r.authMiddleware)

	protected.HandleFunc("/auth/logout", r.authHandler.Logout).Methods("POST", "OPTIONS")
	protected.HandleFunc("/auth/refresh", r.authHandler.RefreshToken).Methods("POST", "OPTIONS")

	// WebSocket endpoint
	r.router.HandleFunc("/ws", r.chatServer.HandleWebSocket)
}

// authMiddleware authenticates requests
func (r *Router) authMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		token := getTokenFromHeader(req)
		if token == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Verify token
		_, err := r.authHandler.authService.VerifyToken(token)
		if err != nil {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		next.ServeHTTP(w, req)
	})
}

// corsMiddleware handles CORS
func (r *Router) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		// Handle preflight requests
		if req.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, req)
	})
}

// ServeHTTP implements the http.Handler interface
func (r *Router) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	r.router.ServeHTTP(w, req)
}

// Start starts the HTTP server
func (r *Router) Start(addr string) error {
	server := &http.Server{
		Addr:         addr,
		Handler:      r,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return server.ListenAndServe()
}
