package auth

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrUserNotFound       = errors.New("user not found")
	ErrUserExists         = errors.New("user already exists")
	ErrInvalidToken       = errors.New("invalid token")
	ErrExpiredToken       = errors.New("token has expired")
)

// User represents a user in the system
type User struct {
	ID           string    `json:"id"`
	Username     string    `json:"username"`
	PasswordHash string    `json:"-"`
	PublicKey    string    `json:"public_key"`
	CreatedAt    time.Time `json:"created_at"`
	LastLogin    time.Time `json:"last_login,omitempty"`
	Status       string    `json:"status"`
	IsActive     bool      `json:"is_active"`
}

// Session represents a user session
type Session struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	IsValid   bool      `json:"is_valid"`
	IPAddress string    `json:"ip_address,omitempty"`
	UserAgent string    `json:"user_agent,omitempty"`
}

// Claims represents the JWT claims
type Claims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// AuthService handles authentication operations
type AuthService struct {
	store      UserStore
	jwtSecret  []byte
	tokenExp   time.Duration
	refreshExp time.Duration
}

// UserStore defines the interface for user persistence
type UserStore interface {
	CreateUser(user *User, password string) error
	GetUserByID(id string) (*User, error)
	GetUserByUsername(username string) (*User, error)
	UpdateUser(user *User) error
	DeleteUser(id string) error
	CreateSession(session *Session) error
	GetSessionByToken(token string) (*Session, error)
	InvalidateSession(id string) error
	InvalidateAllUserSessions(userID string) error
}

// NewAuthService creates a new authentication service
func NewAuthService(store UserStore, jwtSecret string, tokenExp, refreshExp time.Duration) *AuthService {
	return &AuthService{
		store:      store,
		jwtSecret:  []byte(jwtSecret),
		tokenExp:   tokenExp,
		refreshExp: refreshExp,
	}
}

// Register creates a new user
func (a *AuthService) Register(username, password, publicKey string) (*User, error) {
	// Check if user already exists
	_, err := a.store.GetUserByUsername(username)
	if err == nil {
		return nil, ErrUserExists
	}

	// Create new user
	user := &User{
		ID:        uuid.New().String(),
		Username:  username,
		PublicKey: publicKey,
		CreatedAt: time.Now(),
		Status:    "offline",
		IsActive:  true,
	}

	// Store user with hashed password
	if err := a.store.CreateUser(user, password); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// Login authenticates a user and creates a session
func (a *AuthService) Login(username, password string, ipAddress, userAgent string) (*User, string, error) {
	// Get user by username
	user, err := a.store.GetUserByUsername(username)
	if err != nil {
		return nil, "", ErrInvalidCredentials
	}

	// Verify password
	if err := a.verifyPassword(user, password); err != nil {
		return nil, "", ErrInvalidCredentials
	}

	// Generate JWT token
	token, err := a.generateToken(user)
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate token: %w", err)
	}

	// Create session
	session := &Session{
		ID:        uuid.New().String(),
		UserID:    user.ID,
		Token:     token,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(a.refreshExp),
		IsValid:   true,
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	if err := a.store.CreateSession(session); err != nil {
		return nil, "", fmt.Errorf("failed to create session: %w", err)
	}

	// Update last login time
	user.LastLogin = time.Now()
	user.Status = "online"
	if err := a.store.UpdateUser(user); err != nil {
		// Non-critical error, just log it
		fmt.Printf("Failed to update user last login: %v\n", err)
	}

	return user, token, nil
}

// Logout invalidates a user session
func (a *AuthService) Logout(token string) error {
	session, err := a.store.GetSessionByToken(token)
	if err != nil {
		return ErrInvalidToken
	}

	return a.store.InvalidateSession(session.ID)
}

// VerifyToken validates a JWT token and returns the user
func (a *AuthService) VerifyToken(tokenString string) (*User, error) {
	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return a.jwtSecret, nil
	})

	if err != nil {
		return nil, ErrInvalidToken
	}

	// Validate token
	if !token.Valid {
		return nil, ErrInvalidToken
	}

	// Extract claims
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, ErrInvalidToken
	}

	// Check if token is expired
	if claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, ErrExpiredToken
	}

	// Get user from store
	user, err := a.store.GetUserByID(claims.UserID)
	if err != nil {
		return nil, ErrUserNotFound
	}

	return user, nil
}

// RefreshToken generates a new token for a valid refresh token
func (a *AuthService) RefreshToken(refreshToken string) (string, error) {
	// Get session by token
	session, err := a.store.GetSessionByToken(refreshToken)
	if err != nil {
		return "", ErrInvalidToken
	}

	// Check if session is valid
	if !session.IsValid {
		return "", ErrInvalidToken
	}

	// Check if session is expired
	if session.ExpiresAt.Before(time.Now()) {
		return "", ErrExpiredToken
	}

	// Get user
	user, err := a.store.GetUserByID(session.UserID)
	if err != nil {
		return "", ErrUserNotFound
	}

	// Generate new token
	token, err := a.generateToken(user)
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return token, nil
}

// UpdateUserStatus updates a user's status
func (a *AuthService) UpdateUserStatus(userID, status string) error {
	user, err := a.store.GetUserByID(userID)
	if err != nil {
		return ErrUserNotFound
	}

	user.Status = status
	return a.store.UpdateUser(user)
}

// Helper functions

// generateToken creates a new JWT token for a user
func (a *AuthService) generateToken(user *User) (string, error) {
	// Create claims
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(a.tokenExp)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "cypher-chat",
			Subject:   user.ID,
			ID:        uuid.New().String(),
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	return token.SignedString(a.jwtSecret)
}

// verifyPassword checks if the provided password matches the stored hash
func (a *AuthService) verifyPassword(user *User, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
}

// HashPassword creates a bcrypt hash of the password
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

// GenerateRandomKey generates a random key for JWT signing
func GenerateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}
