package auth

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"net"
	"os"
	"time"
)

// CertManager handles certificate operations
type CertManager struct {
	CertFile    string
	KeyFile     string
	Certificate *tls.Certificate
}

// NewCertManager creates a new certificate manager
func NewCertManager(certFile, keyFile string) *CertManager {
	return &CertManager{
		CertFile: certFile,
		KeyFile:  keyFile,
	}
}

// GenerateCert generates a self-signed certificate
func (cm *CertManager) GenerateCert() error {
	// Generate private key
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return fmt.Errorf("failed to generate private key: %v", err)
	}

	// Create certificate template
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"Cypher Chat"},
		},
		NotBefore: time.Now(),
		NotAfter:  time.Now().Add(365 * 24 * time.Hour), // Valid for 1 year
		KeyUsage:  x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage: []x509.ExtKeyUsage{
			x509.ExtKeyUsageServerAuth,
			x509.ExtKeyUsageClientAuth,
		},
		BasicConstraintsValid: true,
		IPAddresses:          []net.IP{net.ParseIP("127.0.0.1")},
		DNSNames:             []string{"localhost"},
	}

	// Create certificate
	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return fmt.Errorf("failed to create certificate: %v", err)
	}

	// Save certificate to file
	certOut, err := os.Create(cm.CertFile)
	if err != nil {
		return fmt.Errorf("failed to open cert.pem for writing: %v", err)
	}
	defer certOut.Close()

	if err := pem.Encode(certOut, &pem.Block{Type: "CERTIFICATE", Bytes: derBytes}); err != nil {
		return fmt.Errorf("failed to write data to cert.pem: %v", err)
	}

	// Save private key to file
	keyOut, err := os.OpenFile(cm.KeyFile, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		return fmt.Errorf("failed to open key.pem for writing: %v", err)
	}
	defer keyOut.Close()

	privBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	if err := pem.Encode(keyOut, &pem.Block{Type: "RSA PRIVATE KEY", Bytes: privBytes}); err != nil {
		return fmt.Errorf("failed to write data to key.pem: %v", err)
	}

	return nil
}

// LoadCert loads the certificate from files
func (cm *CertManager) LoadCert() error {
	cert, err := tls.LoadX509KeyPair(cm.CertFile, cm.KeyFile)
	if err != nil {
		return fmt.Errorf("failed to load certificate: %v", err)
	}
	cm.Certificate = &cert
	return nil
}

// GetTLSConfig returns a TLS configuration using the loaded certificate
func (cm *CertManager) GetTLSConfig() (*tls.Config, error) {
	if cm.Certificate == nil {
		if err := cm.LoadCert(); err != nil {
			return nil, err
		}
	}

	return &tls.Config{
		Certificates: []tls.Certificate{*cm.Certificate},
		ClientAuth:   tls.RequireAndVerifyClientCert,
		MinVersion:   tls.VersionTLS12,
	}, nil
}
