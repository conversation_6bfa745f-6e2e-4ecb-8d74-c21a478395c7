package auth

import (
	"sync"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// MemoryUserStore is a simple in-memory implementation of UserStore for development
type MemoryUserStore struct {
	users    map[string]*User    // userID -> User
	usersByUsername map[string]*User // username -> User
	sessions map[string]*Session // token -> Session
	mutex    sync.RWMutex
}

// NewMemoryUserStore creates a new in-memory user store
func NewMemoryUserStore() *MemoryUserStore {
	return &MemoryUserStore{
		users:           make(map[string]*User),
		usersByUsername: make(map[string]*User),
		sessions:        make(map[string]*Session),
	}
}

// <PERSON><PERSON><PERSON><PERSON> creates a new user with hashed password
func (m *MemoryUserStore) CreateUser(user *User, password string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if user already exists
	if _, exists := m.usersByUsername[user.Username]; exists {
		return ErrUserExists
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// Create user copy with hashed password
	userCopy := *user
	userCopy.PasswordHash = string(hashedPassword)
	userCopy.CreatedAt = time.Now()
	userCopy.Status = "offline"
	userCopy.IsActive = true

	// Store user
	m.users[user.ID] = &userCopy
	m.usersByUsername[user.Username] = &userCopy

	return nil
}

// GetUserByID retrieves a user by ID
func (m *MemoryUserStore) GetUserByID(id string) (*User, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	user, exists := m.users[id]
	if !exists {
		return nil, ErrUserNotFound
	}

	// Return a copy to prevent external modification
	userCopy := *user
	return &userCopy, nil
}

// GetUserByUsername retrieves a user by username
func (m *MemoryUserStore) GetUserByUsername(username string) (*User, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	user, exists := m.usersByUsername[username]
	if !exists {
		return nil, ErrUserNotFound
	}

	// Return a copy to prevent external modification
	userCopy := *user
	return &userCopy, nil
}

// UpdateUser updates an existing user
func (m *MemoryUserStore) UpdateUser(user *User) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Check if user exists
	existingUser, exists := m.users[user.ID]
	if !exists {
		return ErrUserNotFound
	}

	// Update the user while preserving the password hash
	userCopy := *user
	userCopy.PasswordHash = existingUser.PasswordHash

	// Update in both maps
	m.users[user.ID] = &userCopy
	
	// Update username mapping if username changed
	if existingUser.Username != user.Username {
		delete(m.usersByUsername, existingUser.Username)
		m.usersByUsername[user.Username] = &userCopy
	} else {
		m.usersByUsername[user.Username] = &userCopy
	}

	return nil
}

// DeleteUser deletes a user
func (m *MemoryUserStore) DeleteUser(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	user, exists := m.users[id]
	if !exists {
		return ErrUserNotFound
	}

	// Remove from both maps
	delete(m.users, id)
	delete(m.usersByUsername, user.Username)

	// Invalidate all sessions for this user
	for token, session := range m.sessions {
		if session.UserID == id {
			delete(m.sessions, token)
		}
	}

	return nil
}

// CreateSession creates a new session
func (m *MemoryUserStore) CreateSession(session *Session) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Store session
	sessionCopy := *session
	m.sessions[session.Token] = &sessionCopy

	return nil
}

// GetSessionByToken retrieves a session by token
func (m *MemoryUserStore) GetSessionByToken(token string) (*Session, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	session, exists := m.sessions[token]
	if !exists {
		return nil, ErrInvalidToken
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		return nil, ErrExpiredToken
	}

	// Return a copy
	sessionCopy := *session
	return &sessionCopy, nil
}

// InvalidateSession invalidates a session by ID
func (m *MemoryUserStore) InvalidateSession(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Find and remove session by ID
	for token, session := range m.sessions {
		if session.ID == id {
			delete(m.sessions, token)
			return nil
		}
	}

	return ErrInvalidToken
}

// InvalidateAllUserSessions invalidates all sessions for a user
func (m *MemoryUserStore) InvalidateAllUserSessions(userID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Remove all sessions for the user
	for token, session := range m.sessions {
		if session.UserID == userID {
			delete(m.sessions, token)
		}
	}

	return nil
}

// GetStats returns statistics about the memory store (for debugging)
func (m *MemoryUserStore) GetStats() map[string]int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return map[string]int{
		"users":    len(m.users),
		"sessions": len(m.sessions),
	}
}
