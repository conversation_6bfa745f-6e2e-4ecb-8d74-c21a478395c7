package auth

import (
	"database/sql"
	"errors"
	"fmt"
)

// PostgresUserStore implements UserStore interface with PostgreSQL
type PostgresUserStore struct {
	db *sql.DB
}

// NewPostgresUserStore creates a new PostgreSQL user store
func NewPostgresUserStore(db *sql.DB) *PostgresUserStore {
	return &PostgresUserStore{db: db}
}

// <PERSON><PERSON><PERSON><PERSON> creates a new user in the database
func (s *PostgresUserStore) CreateUser(user *User, password string) error {
	// Hash the password
	passwordHash, err := HashPassword(password)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Insert user into database
	query := `
	INSERT INTO users (id, username, password_hash, public_key, created_at, status, is_active)
	VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err = s.db.Exec(query,
		user.ID,
		user.Username,
		passwordHash,
		user.<PERSON><PERSON><PERSON>,
		user.CreatedAt,
		user.Status,
		user.IsActive,
	)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// GetUserByID retrieves a user by ID
func (s *PostgresUserStore) GetUserByID(id string) (*User, error) {
	query := `
	SELECT id, username, password_hash, public_key, created_at, last_login, status, is_active
	FROM users
	WHERE id = $1
	`

	var user User
	var lastLogin sql.NullTime

	err := s.db.QueryRow(query, id).Scan(
		&user.ID,
		&user.Username,
		&user.PasswordHash,
		&user.PublicKey,
		&user.CreatedAt,
		&lastLogin,
		&user.Status,
		&user.IsActive,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if lastLogin.Valid {
		user.LastLogin = lastLogin.Time
	}

	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (s *PostgresUserStore) GetUserByUsername(username string) (*User, error) {
	query := `
	SELECT id, username, password_hash, public_key, created_at, last_login, status, is_active
	FROM users
	WHERE username = $1
	`

	var user User
	var lastLogin sql.NullTime

	err := s.db.QueryRow(query, username).Scan(
		&user.ID,
		&user.Username,
		&user.PasswordHash,
		&user.PublicKey,
		&user.CreatedAt,
		&lastLogin,
		&user.Status,
		&user.IsActive,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if lastLogin.Valid {
		user.LastLogin = lastLogin.Time
	}

	return &user, nil
}

// UpdateUser updates a user in the database
func (s *PostgresUserStore) UpdateUser(user *User) error {
	query := `
	UPDATE users
	SET username = $1, public_key = $2, last_login = $3, status = $4, is_active = $5
	WHERE id = $6
	`

	_, err := s.db.Exec(query,
		user.Username,
		user.PublicKey,
		user.LastLogin,
		user.Status,
		user.IsActive,
		user.ID,
	)
	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	return nil
}

// DeleteUser deletes a user from the database
func (s *PostgresUserStore) DeleteUser(id string) error {
	query := "DELETE FROM users WHERE id = $1"

	_, err := s.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// CreateSession creates a new session in the database
func (s *PostgresUserStore) CreateSession(session *Session) error {
	query := `
	INSERT INTO sessions (id, user_id, token, created_at, expires_at, is_valid, ip_address, user_agent)
	VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := s.db.Exec(query,
		session.ID,
		session.UserID,
		session.Token,
		session.CreatedAt,
		session.ExpiresAt,
		session.IsValid,
		session.IPAddress,
		session.UserAgent,
	)
	if err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	return nil
}

// GetSessionByToken retrieves a session by token
func (s *PostgresUserStore) GetSessionByToken(token string) (*Session, error) {
	query := `
	SELECT id, user_id, token, created_at, expires_at, is_valid, ip_address, user_agent
	FROM sessions
	WHERE token = $1
	`

	var session Session
	err := s.db.QueryRow(query, token).Scan(
		&session.ID,
		&session.UserID,
		&session.Token,
		&session.CreatedAt,
		&session.ExpiresAt,
		&session.IsValid,
		&session.IPAddress,
		&session.UserAgent,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrInvalidToken
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return &session, nil
}

// InvalidateSession marks a session as invalid
func (s *PostgresUserStore) InvalidateSession(id string) error {
	query := "UPDATE sessions SET is_valid = FALSE WHERE id = $1"

	_, err := s.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to invalidate session: %w", err)
	}

	return nil
}

// InvalidateAllUserSessions invalidates all sessions for a user
func (s *PostgresUserStore) InvalidateAllUserSessions(userID string) error {
	query := "UPDATE sessions SET is_valid = FALSE WHERE user_id = $1"

	_, err := s.db.Exec(query, userID)
	if err != nil {
		return fmt.Errorf("failed to invalidate user sessions: %w", err)
	}

	return nil
}
