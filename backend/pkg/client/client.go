package client

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"log"
	"sync"

	"github.com/gorilla/websocket"
	"github.com/v47on/cypher-chat/backend/pkg/crypto"
)

const (
	MessageTypePublicKey = "public_key"
	MessageTypeChat     = "chat"
	MessageTypeClientList = "client_list"
)

// Client represents a chat client
type Client struct {
	conn      *websocket.Conn
	keyPair   *crypto.KeyPair
	mutex     sync.Mutex
	handlers  map[string]func([]byte) error
	clients   map[string][]byte // Map of client IDs to their public keys
	clientId  string
}

// Message represents a chat message
type Message struct {
	Type        string `json:"type"`
	Content     []byte `json:"content"`
	PublicKey   []byte `json:"public_key,omitempty"`
	SenderId    string `json:"sender_id,omitempty"`
	RecipientId string `json:"recipient_id,omitempty"`
}

// NewClient creates a new chat client
func NewClient() (*Client, error) {
	keyPair, err := crypto.GenerateKeyPair(2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate key pair: %v", err)
	}

	client := &Client{
		keyPair:  keyPair,
		handlers: make(map[string]func([]byte) error),
		clients:  make(map[string][]byte),
	}

	// Register message handlers
	client.handlers[MessageTypeChat] = client.handleChatMessage
	client.handlers[MessageTypeClientList] = client.handleClientList

	return client, nil
}

// Connect establishes a WebSocket connection
func (c *Client) Connect(url string) error {
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		return fmt.Errorf("failed to connect: %v", err)
	}

	c.conn = conn

	// Send public key
	pubKey, err := c.keyPair.ExportPublicKey()
	if err != nil {
		return fmt.Errorf("failed to export public key: %v", err)
	}

	msg := Message{
		Type:      MessageTypePublicKey,
		PublicKey: pubKey,
	}

	if err := c.sendMessage(msg); err != nil {
		return fmt.Errorf("failed to send public key: %v", err)
	}

	go c.readMessages()

	return nil
}

// SendMessage sends an encrypted message
func (c *Client) SendMessage(content string, recipientPubKey []byte) error {
	// Parse the public key from PEM format
	block, _ := pem.Decode(recipientPubKey)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block containing public key")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return fmt.Errorf("not an RSA public key")
	}

	// Encrypt message
	encryptedContent, err := crypto.EncryptMessage([]byte(content), rsaPub)
	if err != nil {
		return fmt.Errorf("failed to encrypt message: %v", err)
	}

	msg := Message{
		Type:    MessageTypeChat,
		Content: encryptedContent,
	}

	return c.sendMessage(msg)
}

func (c *Client) sendMessage(msg Message) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	return c.conn.WriteMessage(websocket.TextMessage, data)
}

func (c *Client) readMessages() {
	for {
		_, data, err := c.conn.ReadMessage()
		if err != nil {
			log.Printf("error reading message: %v", err)
			return
		}

		var msg Message
		if err := json.Unmarshal(data, &msg); err != nil {
			log.Printf("error unmarshaling message: %v", err)
			continue
		}

		if handler, ok := c.handlers[msg.Type]; ok {
			if err := handler(msg.Content); err != nil {
				log.Printf("error handling message: %v", err)
			}
		}
	}
}

// SendMessageToClient sends an encrypted message to a specific client
func (c *Client) SendMessageToClient(recipientId string, content string) error {
	c.mutex.Lock()
	recipientPubKey, ok := c.clients[recipientId]
	c.mutex.Unlock()

	if !ok {
		return fmt.Errorf("recipient %s not found", recipientId)
	}

	// Parse the public key from PEM format
	block, _ := pem.Decode(recipientPubKey)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block containing public key")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse public key: %v", err)
	}

	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return fmt.Errorf("not an RSA public key")
	}

	// Encrypt message
	encryptedContent, err := crypto.EncryptMessage([]byte(content), rsaPub)
	if err != nil {
		return fmt.Errorf("failed to encrypt message: %v", err)
	}

	msg := Message{
		Type:        MessageTypeChat,
		Content:     encryptedContent,
		RecipientId: recipientId,
	}

	return c.sendMessage(msg)
}

// Close closes the client connection
func (c *Client) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// handleChatMessage processes incoming chat messages
func (c *Client) handleChatMessage(content []byte) error {
	var msg Message
	if err := json.Unmarshal(content, &msg); err != nil {
		return fmt.Errorf("failed to unmarshal chat message: %v", err)
	}

	// Decrypt message
	decryptedContent, err := crypto.DecryptMessage(msg.Content, c.keyPair.PrivateKey)
	if err != nil {
		return fmt.Errorf("failed to decrypt message: %v", err)
	}

	log.Printf("Message from %s: %s", msg.SenderId, string(decryptedContent))
	return nil
}

// handleClientList processes client list updates
func (c *Client) handleClientList(content []byte) error {
	var clientList map[string][]byte
	if err := json.Unmarshal(content, &clientList); err != nil {
		return fmt.Errorf("failed to unmarshal client list: %v", err)
	}

	c.mutex.Lock()
	c.clients = clientList
	c.mutex.Unlock()

	// Log connected clients (except self)
	log.Printf("\nConnected clients:")
	for id := range clientList {
		if id != c.clientId {
			log.Printf("- %s", id)
		}
	}

	return nil
}
