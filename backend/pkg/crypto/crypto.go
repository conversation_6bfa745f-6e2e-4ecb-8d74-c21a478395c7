package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"io"
)

// KeyPair holds both public and private keys
type KeyPair struct {
	PrivateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

// GenerateKeyPair generates a new key pair
func GenerateKeyPair(bits int) (*KeyPair, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, bits)
	if err != nil {
		return nil, err
	}

	return &KeyPair{
		PrivateKey: privateKey,
		PublicKey:  &privateKey.PublicKey,
	}, nil
}

// ExportPublicKey exports the public key to PEM format
func (kp *KeyPair) ExportPublicKey() ([]byte, error) {
	pubKeyBytes, err := x509.MarshalPKIXPublicKey(kp.PublicKey)
	if err != nil {
		return nil, err
	}

	pubKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: pubKeyBytes,
	})

	return pubKeyPEM, nil
}

// EncryptMessage encrypts a message using RSA-OAEP for the AES key and AES-GCM for the message
func EncryptMessage(message []byte, publicKey *rsa.PublicKey) ([]byte, error) {
	// Generate AES key
	aesKey := make([]byte, 32)
	if _, err := io.ReadFull(rand.Reader, aesKey); err != nil {
		return nil, err
	}

	// Encrypt AES key with RSA
	encryptedAESKey, err := rsa.EncryptOAEP(
		sha256.New(),
		rand.Reader,
		publicKey,
		aesKey,
		nil,
	)
	if err != nil {
		return nil, err
	}

	// Create AES cipher
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// Create nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	// Encrypt message
	ciphertext := gcm.Seal(nonce, nonce, message, nil)

	// Combine encrypted AES key length, encrypted AES key, and ciphertext
	keyLen := len(encryptedAESKey)
	result := make([]byte, 4+keyLen+len(ciphertext))
	
	// Store key length
	result[0] = byte(keyLen >> 24)
	result[1] = byte(keyLen >> 16)
	result[2] = byte(keyLen >> 8)
	result[3] = byte(keyLen)
	
	// Copy encrypted AES key and ciphertext
	copy(result[4:4+keyLen], encryptedAESKey)
	copy(result[4+keyLen:], ciphertext)

	return result, nil
}

// DecryptMessage decrypts a message using the private key
func DecryptMessage(encryptedMsg []byte, privateKey *rsa.PrivateKey) ([]byte, error) {
	if len(encryptedMsg) < 4 {
		return nil, errors.New("message too short")
	}

	// Extract key length
	keyLen := int(encryptedMsg[0])<<24 | int(encryptedMsg[1])<<16 | int(encryptedMsg[2])<<8 | int(encryptedMsg[3])

	if len(encryptedMsg) < 4+keyLen {
		return nil, errors.New("invalid message format")
	}

	// Extract encrypted AES key and ciphertext
	encryptedAESKey := encryptedMsg[4 : 4+keyLen]
	ciphertext := encryptedMsg[4+keyLen:]

	// Decrypt AES key
	aesKey, err := rsa.DecryptOAEP(
		sha256.New(),
		rand.Reader,
		privateKey,
		encryptedAESKey,
		nil,
	)
	if err != nil {
		return nil, err
	}

	// Create AES cipher
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	if len(ciphertext) < gcm.NonceSize() {
		return nil, errors.New("ciphertext too short")
	}

	// Extract nonce and ciphertext
	nonce := ciphertext[:gcm.NonceSize()]
	ciphertext = ciphertext[gcm.NonceSize():]

	// Decrypt message
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// EncryptMessageWithPublicKey encrypts a message using RSA-OAEP for the AES key and AES-GCM for the message
func EncryptMessageWithPublicKey(message []byte, publicKey *rsa.PublicKey) ([]byte, error) {
	// Generate AES key
	aesKey := make([]byte, 32)
	if _, err := io.ReadFull(rand.Reader, aesKey); err != nil {
		return nil, err
	}

	// Encrypt AES key with RSA
	encryptedAESKey, err := rsa.EncryptOAEP(
		sha256.New(),
		rand.Reader,
		publicKey,
		aesKey,
		nil,
	)
	if err != nil {
		return nil, err
	}

	// Create AES cipher
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// Create nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	// Encrypt message
	ciphertext := gcm.Seal(nonce, nonce, message, nil)

	// Combine encrypted AES key length, encrypted AES key, and ciphertext
	keyLen := len(encryptedAESKey)
	result := make([]byte, 4+keyLen+len(ciphertext))
	
	// Store key length
	result[0] = byte(keyLen >> 24)
	result[1] = byte(keyLen >> 16)
	result[2] = byte(keyLen >> 8)
	result[3] = byte(keyLen)
	
	// Copy encrypted AES key and ciphertext
	copy(result[4:4+keyLen], encryptedAESKey)
	copy(result[4+keyLen:], ciphertext)

	return result, nil
}

// EncryptMessageWithKey encrypts a message using an existing AES key
func EncryptMessageWithKey(message []byte, key []byte) ([]byte, error) {
	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// Generate nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, err
	}

	// Encrypt message
	return gcm.Seal(nonce, nonce, message, nil), nil
}
