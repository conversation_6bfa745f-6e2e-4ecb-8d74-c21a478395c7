package group

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"sync"
	"time"
)

// Group represents a chat group
type Group struct {
	ID          string
	Name        string
	Members     map[string]bool
	Admins      map[string]bool
	CreatedAt   time.Time
	Description string
	mutex       sync.RWMutex
}

// GroupManager manages multiple groups
type GroupManager struct {
	groups map[string]*Group
	mutex  sync.RWMutex
}

// NewGroupManager creates a new group manager
func NewGroupManager() *GroupManager {
	return &GroupManager{
		groups: make(map[string]*Group),
	}
}

// CreateGroup creates a new group
func (gm *GroupManager) CreateGroup(name, description string, creator string) (*Group, error) {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()

	// Generate unique group ID
	groupID, err := generateGroupID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate group ID: %v", err)
	}

	group := &Group{
		ID:          groupID,
		Name:        name,
		Description: description,
		Members:     make(map[string]bool),
		Admins:      make(map[string]bool),
		CreatedAt:   time.Now(),
	}

	// Add creator as admin and member
	group.Members[creator] = true
	group.Admins[creator] = true

	gm.groups[groupID] = group
	return group, nil
}

// GetGroup retrieves a group by ID
func (gm *GroupManager) GetGroup(groupID string) (*Group, error) {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()

	group, exists := gm.groups[groupID]
	if !exists {
		return nil, fmt.Errorf("group not found: %s", groupID)
	}
	return group, nil
}

// AddMember adds a new member to the group
func (g *Group) AddMember(adminID, newMemberID string) error {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// Check if admin exists and has permission
	if !g.Admins[adminID] {
		return fmt.Errorf("only admins can add members")
	}

	// Check if member already exists
	if g.Members[newMemberID] {
		return fmt.Errorf("member already in group")
	}

	g.Members[newMemberID] = true
	return nil
}

// RemoveMember removes a member from the group
func (g *Group) RemoveMember(adminID, memberID string) error {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// Check if admin exists and has permission
	if !g.Admins[adminID] {
		return fmt.Errorf("only admins can remove members")
	}

	// Check if member exists
	if !g.Members[memberID] {
		return fmt.Errorf("member not found in group")
	}

	// Prevent removing the last admin
	if g.Admins[memberID] && len(g.Admins) == 1 {
		return fmt.Errorf("cannot remove the last admin")
	}

	delete(g.Members, memberID)
	delete(g.Admins, memberID)
	return nil
}

// AddAdmin promotes a member to admin
func (g *Group) AddAdmin(currentAdminID, newAdminID string) error {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	// Check if current admin exists and has permission
	if !g.Admins[currentAdminID] {
		return fmt.Errorf("only admins can add new admins")
	}

	// Check if new admin is a member
	if !g.Members[newAdminID] {
		return fmt.Errorf("user must be a group member to become an admin")
	}

	g.Admins[newAdminID] = true
	return nil
}

// ListMembers returns a list of group members
func (g *Group) ListMembers() []string {
	g.mutex.RLock()
	defer g.mutex.RUnlock()

	members := make([]string, 0, len(g.Members))
	for member := range g.Members {
		members = append(members, member)
	}
	return members
}

// ListAdmins returns a list of group admins
func (g *Group) ListAdmins() []string {
	g.mutex.RLock()
	defer g.mutex.RUnlock()

	admins := make([]string, 0, len(g.Admins))
	for admin := range g.Admins {
		admins = append(admins, admin)
	}
	return admins
}

// generateGroupID creates a unique, random group ID
func generateGroupID() (string, error) {
	b := make([]byte, 12) // 12 bytes = 16 base64 characters
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}
