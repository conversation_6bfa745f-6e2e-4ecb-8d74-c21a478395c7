package ratelimit

import (
	"fmt"
	"sync"
	"time"
)

// RateLimiter manages rate limiting for different types of actions
type RateLimiter struct {
	limits     map[string]*actionLimiter
	globalLock sync.RWMutex
	mutex      sync.Mutex
}

// actionLimiter tracks rate limits for a specific action
type actionLimiter struct {
	maxRequests int
	interval    time.Duration
	requests    []time.Time
	lock        sync.Mutex
	count       int
	lastReset   time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter() *RateLimiter {
	return &RateLimiter{
		limits: make(map[string]*actionLimiter),
	}
}

// DefineLimit sets a rate limit for a specific action type
func (rl *RateLimiter) DefineLimit(actionType string, maxRequests int, interval time.Duration) {
	rl.globalLock.Lock()
	defer rl.globalLock.Unlock()

	rl.limits[actionType] = &actionLimiter{
		maxRequests: maxRequests,
		interval:    interval,
		requests:    []time.Time{},
	}
}

// Allow checks if an action is allowed based on rate limits
func (rl *RateLimiter) Allow(clientID string, action string) error {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()

	now := time.Now()
	key := fmt.Sprintf("%s:%s", clientID, action)
	
	// Clean up old entries
	for k, v := range rl.limits {
		if now.Sub(v.lastReset) > time.Hour {
			delete(rl.limits, k)
		}
	}

	limit, exists := rl.limits[key]
	if !exists {
		rl.limits[key] = &actionLimiter{
			count:     1,
			lastReset: now,
		}
		return nil
	}

	// Reset counter if time window has passed
	if now.Sub(limit.lastReset) > time.Minute {
		limit.count = 1
		limit.lastReset = now
		return nil
	}

	// Check if limit exceeded
	if limit.count >= 100 { // 100 actions per minute
		return fmt.Errorf("rate limit exceeded for %s", action)
	}

	limit.count++
	return nil
}

// ClientRateLimiter provides per-client rate limiting
type ClientRateLimiter struct {
	clients     map[string]*ClientLimits
	globalLimit *RateLimiter
	lock        sync.RWMutex
}

// ClientLimits tracks rate limits for a specific client
type ClientLimits struct {
	messageCount int
	fileCount    int
	lastReset    time.Time
}

// NewClientRateLimiter creates a new client rate limiter
func NewClientRateLimiter() *ClientRateLimiter {
	return &ClientRateLimiter{
		clients:     make(map[string]*ClientLimits),
		globalLimit: NewRateLimiter(),
	}
}

// DefineGlobalLimit sets a global rate limit
func (crl *ClientRateLimiter) DefineGlobalLimit(actionType string, maxRequests int, interval time.Duration) {
	crl.globalLimit.DefineLimit(actionType, maxRequests, interval)
}

// AllowClientAction checks if a client is allowed to perform an action
func (crl *ClientRateLimiter) AllowClientAction(clientID, actionType string) error {
	// First check global rate limit
	if err := crl.globalLimit.Allow(clientID, actionType); err != nil {
		return err
	}

	crl.lock.Lock()
	defer crl.lock.Unlock()

	// Get or create client limits
	clientLimits, exists := crl.clients[clientID]
	if !exists {
		clientLimits = &ClientLimits{
			lastReset: time.Now(),
		}
		crl.clients[clientID] = clientLimits
	}

	// Reset limits if more than an hour has passed
	if time.Since(clientLimits.lastReset) > time.Hour {
		clientLimits.messageCount = 0
		clientLimits.fileCount = 0
		clientLimits.lastReset = time.Now()
	}

	// Specific limits for different action types
	switch actionType {
	case "message":
		if clientLimits.messageCount >= 100 { // 100 messages per hour
			return fmt.Errorf("message limit exceeded for client %s", clientID)
		}
		clientLimits.messageCount++
	case "file_upload":
		if clientLimits.fileCount >= 10 { // 10 file uploads per hour
			return fmt.Errorf("file upload limit exceeded for client %s", clientID)
		}
		clientLimits.fileCount++
	default:
		return fmt.Errorf("unknown action type: %s", actionType)
	}

	return nil
}

// ResetClientLimits manually resets a client's rate limits
func (crl *ClientRateLimiter) ResetClientLimits(clientID string) {
	crl.lock.Lock()
	defer crl.lock.Unlock()

	delete(crl.clients, clientID)
}
