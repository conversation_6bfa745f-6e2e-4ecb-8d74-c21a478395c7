package server

import (
	"encoding/json"
	"net/http"
)

func (s *Server) HandleCreateSession(w http.ResponseWriter, r *http.Request) {
	session := s.createSession()
	json.NewEncoder(w).Encode(map[string]interface{}{
		"sessionId": session.ID,
		"expiresAt": session.ExpiresAt,
	})
}

func (s *Server) HandleJoinSession(w http.ResponseWriter, r *http.Request) {
	sessionID := r.URL.Query().Get("id")
	if sessionID == "" {
		http.Error(w, "Missing session ID", http.StatusBadRequest)
		return
	}

	if _, ok := s.sessions.Load(sessionID); !ok {
		http.Error(w, "Session not found", http.StatusNotFound)
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "ok",
		"sessionId": sessionID,
	})
} 