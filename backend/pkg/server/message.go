package server

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/v47on/cypher-chat/backend/pkg/storage"
)

func (c *Client) handleWebSocketMessage(messageType int, payload []byte) {
	var msg Message
	if err := json.Unmarshal(payload, &msg); err != nil {
		log.Printf("Failed to parse message: %v", err)
		return
	}

	switch msg.Type {
	case MessageTypePublicKey:
		c.handlePublicKey(msg)
	case MessageTypeChat:
		c.handleChatMessage(msg)
	case MessageTypeTyping:
		c.handleTypingMessage(msg)
	case MessageTypeDeliveryReceipt:
		c.handleDeliveryReceipt(msg)
	default:
		log.Printf("Unknown message type: %s", msg.Type)
	}
}

func (c *Client) handlePublicKey(msg Message) {
	// Log the raw message content for debugging
	log.Printf("Public key message content: %s", string(msg.Content))

	// Try to parse the content directly
	var content struct {
		Key string `json:"key"`
	}
	if err := json.Unmarshal(msg.Content, &content); err != nil {
		log.Printf("Failed to parse public key message: %v", err)
		return
	}

	// Store the public key
	c.PublicKey = content.Key
	log.Printf("Successfully stored public key for client %s", c.ID)

	// Broadcast updated client list
	c.Server.broadcastClientList()
}

func (c *Client) handleChatMessage(msg Message) {
	var content struct {
		Content     string `json:"content"`
		RecipientId string `json:"recipient_id"`
	}
	if err := json.Unmarshal(msg.Content, &content); err != nil {
		return
	}

	// Store the message in the database
	storageMsg := storage.Message{
		SenderId:    c.ID,
		RecipientId: content.RecipientId,
		Content:     []byte(content.Content),
		Type:        "chat",
		Timestamp:   time.Now(),
		Delivered:   false,
		Read:        false,
	}

	if err := c.Server.store.StoreMessage(storageMsg); err != nil {
		log.Printf("Failed to store message: %v", err)
	}

	// Forward encrypted message to recipient
	if recipient, ok := c.Server.findClient(content.RecipientId); ok {
		msg.SenderId = c.ID
		msg.Timestamp = time.Now()
		data, err := json.Marshal(msg)
		if err != nil {
			log.Printf("Failed to marshal message: %v", err)
			return
		}
		recipient.send <- data
	}
}

func (c *Client) handleTypingMessage(msg Message) {
	var content struct {
		RecipientId string `json:"recipient_id"`
		IsTyping    bool   `json:"is_typing"`
	}
	if err := json.Unmarshal(msg.Content, &content); err != nil {
		log.Printf("Failed to parse typing message: %v", err)
		return
	}

	log.Printf("Typing indicator from %s to %s: %v", c.ID, content.RecipientId, content.IsTyping)

	msg.SenderId = c.ID
	msg.Timestamp = time.Now()
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("Failed to marshal typing message: %v", err)
		return
	}

	// Forward to recipient
	if recipient, ok := c.Server.findClient(content.RecipientId); ok {
		recipient.send <- data
	}
}

func (c *Client) handleDeliveryReceipt(msg Message) {
	var content struct {
		MessageId   string `json:"message_id"`
		Status      string `json:"status"` // "delivered" or "read"
		RecipientId string `json:"recipient_id"`
	}
	if err := json.Unmarshal(msg.Content, &content); err != nil {
		log.Printf("Failed to parse delivery receipt: %v", err)
		return
	}

	log.Printf("Message %s marked as %s by %s", content.MessageId, content.Status, c.ID)

	// Update message status in database
	if content.Status == "delivered" {
		if err := c.Server.store.MarkMessageDelivered(content.MessageId); err != nil {
			log.Printf("Failed to mark message as delivered: %v", err)
		}
	} else if content.Status == "read" {
		if err := c.Server.store.MarkMessageRead(content.MessageId); err != nil {
			log.Printf("Failed to mark message as read: %v", err)
		}
	}

	// Forward receipt to original sender
	if content.RecipientId != "" {
		if recipient, ok := c.Server.findClient(content.RecipientId); ok {
			msg.SenderId = c.ID
			msg.Timestamp = time.Now()
			data, err := json.Marshal(msg)
			if err != nil {
				log.Printf("Failed to marshal delivery receipt: %v", err)
				return
			}
			recipient.send <- data
		}
	}
}

func (c *Client) broadcastClientList() {
	var clients []map[string]interface{}

	c.Server.clients.Range(func(key, value interface{}) bool {
		client := value.(*Client)
		if client.PublicKey != "" {
			clients = append(clients, map[string]interface{}{
				"id":        client.ID,
				"publicKey": client.PublicKey,
				"isOnline":  true,
			})
		}
		return true
	})

	msg := Message{
		Type: MessageTypeClientList,
		Content: json.RawMessage(fmt.Sprintf(`{
			"clients": %s
		}`, string(mustJSON(clients)))),
		Timestamp: time.Now(),
	}

	data, _ := json.Marshal(msg)
	c.Server.clients.Range(func(_, value interface{}) bool {
		client := value.(*Client)
		client.send <- data
		return true
	})
}
