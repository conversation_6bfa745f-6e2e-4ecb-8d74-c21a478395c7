package server

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/v47on/cypher-chat/backend/pkg/ratelimit"
	"github.com/v47on/cypher-chat/backend/pkg/storage"
)

// Allowed origins for WebSocket connections
var allowedOrigins = map[string]bool{
	"http://localhost:3000":  true, // Development
	"https://localhost:3000": true, // Development with HTTPS
	// Add production origins here
}

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		origin := r.Header.Get("Origin")
		log.Printf("WebSocket connection attempt from origin: %s", origin)

		// Check if origin is allowed
		if allowed, exists := allowedOrigins[origin]; exists && allowed {
			return true
		}

		log.Printf("WebSocket connection rejected from unauthorized origin: %s", origin)
		return false
	},
	EnableCompression: true,
	HandshakeTimeout:  10 * time.Second,
}

const (
	maxMessageSize             = 1024 * 1024 * 5 // 5MB
	pongWait                   = 60 * time.Second
	MessageTypePublicKey       = "public_key"
	MessageTypeChat            = "chat"
	MessageTypeClientList      = "client_list"
	MessageTypeTyping          = "typing"
	MessageTypeDeliveryReceipt = "delivery_receipt"
)

// Client represents a connected chat client
type Client struct {
	ID        string          `json:"id"`
	PublicKey string          `json:"public_key"`
	Session   *Session        `json:"-"`
	Conn      *websocket.Conn `json:"-"`
	send      chan []byte     `json:"-"`
	Server    *Server         `json:"-"`
}

// MessageStorage defines the interface for message persistence
type MessageStorage interface {
	StoreMessage(msg storage.Message) error
	GetUndeliveredMessages(recipientId string) ([]*storage.Message, error)
	Close() error
}

// Server represents the chat server
type Server struct {
	clients       sync.Map
	sessions      sync.Map
	store         storage.Store
	register      chan *Client
	unregister    chan *Client
	broadcast     chan []byte
	rateLimiter   *ratelimit.RateLimiter
	maxClients    int
	clientCount   int64
	clientCountMu sync.RWMutex
}

// Session represents a chat session
type Session struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	Clients   sync.Map  `json:"-"`
}

// DeliveryInfo tracks message delivery status
type DeliveryInfo struct {
	Delivered bool      `json:"delivered"`
	ReadAt    time.Time `json:"read_at,omitempty"`
}

// Message represents a chat message
type Message struct {
	Type         string          `json:"type"`
	Content      json.RawMessage `json:"content,omitempty"`
	PublicKey    []byte          `json:"public_key,omitempty"`
	SenderId     string          `json:"sender_id,omitempty"`
	RecipientId  string          `json:"recipient_id,omitempty"`
	GroupId      string          `json:"group_id,omitempty"`
	Timestamp    time.Time       `json:"timestamp"`
	DeliveryInfo DeliveryInfo    `json:"delivery_info,omitempty"`
}

// FileMessage represents a file transfer message
type FileMessage struct {
	Filename    string `json:"filename"`
	FileSize    int64  `json:"file_size"`
	FileType    string `json:"file_type"`
	FileContent []byte `json:"file_content"`
	SenderId    string `json:"sender_id"`
	RecipientId string `json:"recipient_id"`
}

// KeyData represents key validation data
type KeyData struct {
	ID                string `json:"id"`
	ExportedPublicKey string `json:"exportedPublicKey"`
}

// NewServer creates a new chat server
func NewServer(store storage.Store) *Server {
	// Initialize rate limiter
	rateLimiter := ratelimit.NewRateLimiter()

	// Define rate limits
	rateLimiter.DefineLimit("message", 100, time.Minute)   // 100 messages per minute
	rateLimiter.DefineLimit("connection", 10, time.Minute) // 10 connections per minute
	rateLimiter.DefineLimit("public_key", 5, time.Minute)  // 5 key exchanges per minute

	server := &Server{
		store:       store,
		register:    make(chan *Client),
		unregister:  make(chan *Client),
		broadcast:   make(chan []byte),
		rateLimiter: rateLimiter,
		maxClients:  1000, // Maximum concurrent connections
	}

	go server.run()
	return server
}

// Run starts the server's main event loop
func (s *Server) run() {
	for {
		select {
		case client := <-s.register:
			s.handleClientRegistration(client)
		case client := <-s.unregister:
			s.handleClientUnregistration(client)
		case message := <-s.broadcast:
			s.broadcastMessage(message)
		}
	}
}

func (s *Server) broadcastMessage(message []byte) {
	s.clients.Range(func(key, value interface{}) bool {
		client := value.(*Client)
		select {
		case client.send <- message:
		default:
			s.clients.Delete(key)
			close(client.send)
		}
		return true
	})
}

// handleClientRegistration handles new client connections
func (s *Server) handleClientRegistration(client *Client) {
	// Check for existing client
	if existingClient, ok := s.clients.Load(client.ID); ok {
		existing := existingClient.(*Client)
		existing.Conn.Close()
		s.clients.Delete(client.ID)
		// Don't decrement count since we're replacing, not adding
	} else {
		// Increment client count for new connections
		s.clientCountMu.Lock()
		s.clientCount++
		currentCount := s.clientCount
		s.clientCountMu.Unlock()
		log.Printf("Client count increased to: %d", currentCount)
	}

	// Store new client
	s.clients.Store(client.ID, client)
	log.Printf("Client registered: %s", client.ID)

	// Broadcast updated client list
	go s.broadcastClientList()
}

// handleClientUnregistration handles client disconnections
func (s *Server) handleClientUnregistration(client *Client) {
	if _, ok := s.clients.Load(client.ID); ok {
		s.clients.Delete(client.ID)
		close(client.send)

		// Decrement client count
		s.clientCountMu.Lock()
		s.clientCount--
		currentCount := s.clientCount
		s.clientCountMu.Unlock()

		log.Printf("Client unregistered: %s (count: %d)", client.ID, currentCount)
		go s.broadcastClientList()
	}
}

// HandleWebSocket handles incoming WebSocket connections
func (s *Server) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	// Get client IP for rate limiting
	clientIP := getClientIP(r)

	// Check connection rate limit
	if err := s.rateLimiter.Allow(clientIP, "connection"); err != nil {
		log.Printf("Connection rate limit exceeded for IP %s: %v", clientIP, err)
		http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
		return
	}

	// Check maximum client limit
	s.clientCountMu.RLock()
	currentCount := s.clientCount
	s.clientCountMu.RUnlock()

	if currentCount >= int64(s.maxClients) {
		log.Printf("Maximum client limit reached: %d", s.maxClients)
		http.Error(w, "Server at capacity", http.StatusServiceUnavailable)
		return
	}

	// Extract userId from query parameters
	userID := r.URL.Query().Get("userId")
	if userID == "" {
		log.Printf("WebSocket connection rejected: missing userId parameter")
		http.Error(w, "Missing userId parameter", http.StatusBadRequest)
		return
	}

	log.Printf("WebSocket connection request for userId: %s from %s", userID, clientIP)

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	clientID := generateClientId()
	client := &Client{
		ID:     clientID,
		Conn:   conn,
		send:   make(chan []byte, 256),
		Server: s,
	}

	sessionID := r.URL.Query().Get("session")
	if sessionID != "" {
		if sessionVal, ok := s.sessions.Load(sessionID); ok {
			client.Session = sessionVal.(*Session)
		}
	}

	if client.Session == nil {
		client.Session = s.createSession()
	}

	s.register <- client

	go client.readPump()
	go client.writePump()
}

// generateClientId generates a unique client ID
func generateClientId() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

func (c *Client) readPump() {
	defer func() {
		c.Server.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("error reading message: %v", err)
			}
			break
		}

		// Log raw message for debugging
		log.Printf("Raw message received from client %s: %s", c.ID, string(message))

		go func(msg []byte) {
			var msgObj Message
			if err := json.Unmarshal(msg, &msgObj); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				errorMsg := Message{
					Type:      "error",
					Content:   json.RawMessage(`"Invalid message format"`),
					Timestamp: time.Now(),
				}
				if data, err := json.Marshal(errorMsg); err == nil {
					c.send <- data
				}
				return
			}

			// Process message based on type
			switch msgObj.Type {
			case MessageTypePublicKey:
				c.handlePublicKey(msgObj)
			case MessageTypeChat:
				c.handleChatMessage(msgObj)
			case MessageTypeTyping:
				c.handleTypingMessage(msgObj)
			case MessageTypeDeliveryReceipt:
				c.handleDeliveryReceipt(msgObj)
			default:
				log.Printf("Unknown message type: %s", msgObj.Type)
			}
		}(message)
	}
}

func (c *Client) writePump() {
	for message := range c.send {
		if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
			log.Printf("error writing message: %v", err)
			return
		}
	}
	c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
}

func (s *Server) findClient(clientId string) (*Client, bool) {
	if client, ok := s.clients.Load(clientId); ok {
		return client.(*Client), true
	}
	return nil, false
}

func (s *Server) broadcastClientList() {
	// Initialize with empty array instead of nil
	clientList := []map[string]interface{}{}

	s.clients.Range(func(key, value interface{}) bool {
		client := value.(*Client)
		if client.PublicKey != "" {
			clientList = append(clientList, map[string]interface{}{
				"id":         client.ID,
				"public_key": client.PublicKey,
				"is_online":  true,
				"status":     "online",
			})
		}
		return true
	})

	// Log the client list for debugging
	log.Printf("Broadcasting client list with %d clients", len(clientList))
	for i, client := range clientList {
		log.Printf("Client %d: id=%s, has_public_key=%v",
			i, client["id"], client["public_key"] != "")
	}

	msg := struct {
		Type      string                   `json:"type"`
		Clients   []map[string]interface{} `json:"clients"`
		Timestamp time.Time                `json:"timestamp"`
	}{
		Type:      MessageTypeClientList,
		Clients:   clientList,
		Timestamp: time.Now(),
	}

	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("Failed to marshal client list: %v", err)
		return
	}

	// Send to all clients
	s.clients.Range(func(_, value interface{}) bool {
		client := value.(*Client)
		select {
		case client.send <- data:
			// Message sent successfully
		default:
			// Client's send buffer is full, close connection
			s.unregister <- client
		}
		return true
	})
}

// Helper function to marshal JSON without error
func mustJSON(v interface{}) string {
	data, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return string(data)
}

// getClientIP extracts the real client IP from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first (for proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	if idx := strings.LastIndex(r.RemoteAddr, ":"); idx != -1 {
		return r.RemoteAddr[:idx]
	}
	return r.RemoteAddr
}

// Start starts the chat server on the specified address
func (s *Server) Start(addr string) error {
	// Add test endpoint
	http.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Header().Set("Access-Control-Allow-Origin", "*")
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "ok",
			"message": "Server is running",
		})
	})

	http.HandleFunc("/ws", s.HandleWebSocket)
	http.HandleFunc("/health", s.handleHealthCheck)

	log.Printf("Starting WebSocket server on %s", addr)
	return http.ListenAndServe(addr, nil)
}

// Add error handling middleware
func (s *Server) errorHandler(handler http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if err := recover(); err != nil {
				log.Printf("Panic recovered: %v", err)
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()
		handler(w, r)
	}
}

// Add this function to handle health checks
func (s *Server) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	clientCount := 0
	s.clients.Range(func(_, _ interface{}) bool {
		clientCount++
		return true
	})

	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now(),
		"clients":   clientCount,
	})
}

// Add session management methods
func (s *Server) createSession() *Session {
	session := &Session{
		ID:        generateSessionID(),
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}
	s.sessions.Store(session.ID, session)
	return session
}

func generateSessionID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

func (c *Client) sendSessionInfo() {
	msg := Message{
		Type: "session_info",
		Content: json.RawMessage(fmt.Sprintf(`{
			"session_id": "%s",
			"client_id": "%s",
			"expires_at": "%s"
		}`, c.Session.ID, c.ID, c.Session.ExpiresAt.Format(time.RFC3339))),
		Timestamp: time.Now(),
	}

	data, _ := json.Marshal(msg)
	c.send <- data
}
