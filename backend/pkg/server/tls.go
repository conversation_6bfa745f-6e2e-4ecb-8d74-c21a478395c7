package server

import (
    "crypto/tls"
    "log"
)

func configureTLS(certFile, keyFile string) (*tls.Config, error) {
    if certFile == "" || keyFile == "" {
        log.Printf("Warning: TLS disabled - no cert/key files provided")
        return nil, nil
    }

    cert, err := tls.LoadX509KeyPair(certFile, keyFile)
    if err != nil {
        return nil, err
    }

    return &tls.Config{
        Certificates: []tls.Certificate{cert},
        MinVersion:  tls.VersionTLS12,
    }, nil
} 