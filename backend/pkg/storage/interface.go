package storage

// Store defines the interface for message persistence
type Store interface {
	// Message operations
	StoreMessage(msg Message) error
	GetUndeliveredMessages(recipientId string) ([]*Message, error)
	MarkMessageDelivered(messageId string) error
	MarkMessageRead(messageId string) error
	GetGroupMessages(groupId string, limit int, offset int) ([]*Message, error)
	GetMessageHistory(userId string, limit int) ([]*Message, error)
	SaveMessage(msg Message) error

	// Optimized operations
	GetConversation(user1Id, user2Id string, limit, offset int) ([]*Message, error)
	MarkMessagesAsRead(recipientId, senderId string) (int, error)

	// Maintenance operations
	CleanupExpiredSessions() (int, error)
	GetDatabaseStats() (map[string]interface{}, error)

	// Close the database connection
	Close() error
}
