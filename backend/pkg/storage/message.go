package storage

import (
	"encoding/json"
	"time"
)

// Message represents a stored message
type Message struct {
	ID          string    `json:"id"`
	SenderId    string    `json:"sender_id"`
	RecipientId string    `json:"recipient_id"`
	Content     []byte    `json:"content"`
	Type        string    `json:"type"`
	GroupId     string    `json:"group_id,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	Delivered   bool      `json:"delivered"`
	Read        bool      `json:"read"`
}

// MarshalJSON marshals a Message to JSON
func (m *Message) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		ID          string    `json:"id"`
		SenderId    string    `json:"sender_id"`
		RecipientId string    `json:"recipient_id"`
		Content     []byte    `json:"content"`
		Type        string    `json:"type"`
		GroupId     string    `json:"group_id,omitempty"`
		Timestamp   time.Time `json:"timestamp"`
		Delivered   bool      `json:"delivered"`
		Read        bool      `json:"read"`
	}{
		ID:          m.ID,
		SenderId:    m.SenderId,
		RecipientId: m.RecipientId,
		Content:     m.Content,
		Type:        m.Type,
		GroupId:     m.GroupId,
		Timestamp:   m.Timestamp,
		Delivered:   m.Delivered,
		Read:        m.Read,
	})
}
