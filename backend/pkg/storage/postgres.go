package storage

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

// PostgresStore handles message persistence with PostgreSQL
type PostgresStore struct {
	db *sql.DB
}

// NewPostgresStore creates a new PostgreSQL-based message store
func NewPostgresStore(host, port, user, password, dbname string) (*PostgresStore, error) {
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	// Set optimized connection pool parameters
	db.SetMaxOpenConns(50)                  // Increased for better concurrency
	db.SetMaxIdleConns(10)                  // More idle connections for faster response
	db.SetConnMaxLifetime(30 * time.Minute) // Longer lifetime for stability
	db.SetConnMaxIdleTime(5 * time.Minute)  // Close idle connections after 5 minutes

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	log.Println("Successfully connected to PostgreSQL database")
	return &PostgresStore{db: db}, nil
}

// Close closes the database connection
func (ps *PostgresStore) Close() error {
	if ps.db != nil {
		return ps.db.Close()
	}
	return nil
}

// StoreMessage stores a new message
func (ps *PostgresStore) StoreMessage(msg Message) error {
	query := `
	INSERT INTO messages (
		id, sender_id, recipient_id, content, message_type,
		group_id, timestamp, delivered, read
	) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	// Generate a UUID for the message
	id := uuid.New()

	_, err := ps.db.Exec(query,
		id,
		msg.SenderId,
		msg.RecipientId,
		msg.Content,
		msg.Type,
		msg.GroupId,
		msg.Timestamp,
		msg.Delivered,
		msg.Read,
	)
	if err != nil {
		return fmt.Errorf("failed to store message: %v", err)
	}

	return nil
}

// GetUndeliveredMessages retrieves undelivered messages for a recipient
func (ps *PostgresStore) GetUndeliveredMessages(recipientId string) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE recipient_id = $1 AND delivered = FALSE
	ORDER BY timestamp ASC
	`

	rows, err := ps.db.Query(query, recipientId)
	if err != nil {
		return nil, fmt.Errorf("failed to query messages: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id,
			&msg.SenderId,
			&msg.RecipientId,
			&msg.Content,
			&msg.Type,
			&msg.GroupId,
			&msg.Timestamp,
			&msg.Delivered,
			&msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}

		// Convert string ID to int64 for compatibility with existing code
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// MarkMessageDelivered marks a message as delivered
func (ps *PostgresStore) MarkMessageDelivered(messageId string) error {
	query := "UPDATE messages SET delivered = TRUE WHERE id = $1"
	_, err := ps.db.Exec(query, messageId)
	if err != nil {
		return fmt.Errorf("failed to mark message as delivered: %v", err)
	}
	return nil
}

// MarkMessageRead marks a message as read
func (ps *PostgresStore) MarkMessageRead(messageId string) error {
	query := "UPDATE messages SET read = TRUE WHERE id = $1"
	_, err := ps.db.Exec(query, messageId)
	if err != nil {
		return fmt.Errorf("failed to mark message as read: %v", err)
	}
	return nil
}

// GetGroupMessages retrieves messages for a group
func (ps *PostgresStore) GetGroupMessages(groupId string, limit int, offset int) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE group_id = $1
	ORDER BY timestamp DESC
	LIMIT $2 OFFSET $3
	`

	rows, err := ps.db.Query(query, groupId, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query group messages: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id,
			&msg.SenderId,
			&msg.RecipientId,
			&msg.Content,
			&msg.Type,
			&msg.GroupId,
			&msg.Timestamp,
			&msg.Delivered,
			&msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}

		// Convert string ID to int64 for compatibility with existing code
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// GetMessageHistory retrieves chat history for a user (optimized with index)
func (ps *PostgresStore) GetMessageHistory(userId string, limit int) ([]*Message, error) {
	// Use UNION ALL for better performance with separate indexes
	query := `
	(SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	 FROM messages
	 WHERE sender_id = $1
	 ORDER BY timestamp DESC
	 LIMIT $2)
	UNION ALL
	(SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	 FROM messages
	 WHERE recipient_id = $1 AND sender_id != $1
	 ORDER BY timestamp DESC
	 LIMIT $2)
	ORDER BY timestamp DESC
	LIMIT $2
	`

	rows, err := ps.db.Query(query, userId, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query message history: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id,
			&msg.SenderId,
			&msg.RecipientId,
			&msg.Content,
			&msg.Type,
			&msg.GroupId,
			&msg.Timestamp,
			&msg.Delivered,
			&msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}

		// Convert string ID to int64 for compatibility with existing code
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// SaveMessage saves a message to the database
func (ps *PostgresStore) SaveMessage(msg Message) error {
	query := `
	INSERT INTO messages (
		id, sender_id, recipient_id, content, message_type,
		timestamp, delivered, read, group_id
	) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	// Generate a UUID for the message
	id := uuid.New()

	_, err := ps.db.Exec(query,
		id,
		msg.SenderId,
		msg.RecipientId,
		msg.Content,
		msg.Type,
		msg.Timestamp,
		msg.Delivered,
		msg.Read,
		msg.GroupId,
	)
	if err != nil {
		return fmt.Errorf("failed to save message: %v", err)
	}

	return nil
}

// GetConversation retrieves conversation between two users (optimized)
func (ps *PostgresStore) GetConversation(user1Id, user2Id string, limit, offset int) ([]*Message, error) {
	// Use the optimized database function
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, timestamp, delivered, read
	FROM get_conversation($1::UUID, $2::UUID, $3, $4)
	`

	rows, err := ps.db.Query(query, user1Id, user2Id, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query conversation: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		message := &Message{}
		err := rows.Scan(
			&message.ID,
			&message.SenderId,
			&message.RecipientId,
			&message.Content,
			&message.Type,
			&message.Timestamp,
			&message.Delivered,
			&message.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan conversation message: %v", err)
		}
		messages = append(messages, message)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating conversation rows: %v", err)
	}

	return messages, nil
}

// MarkMessagesAsRead marks messages as read efficiently
func (ps *PostgresStore) MarkMessagesAsRead(recipientId, senderId string) (int, error) {
	var query string
	var args []interface{}

	if senderId == "" {
		// Mark all unread messages for recipient
		query = `SELECT mark_messages_read($1::UUID)`
		args = []interface{}{recipientId}
	} else {
		// Mark messages from specific sender
		query = `SELECT mark_messages_read($1::UUID, $2::UUID)`
		args = []interface{}{recipientId, senderId}
	}

	var updatedCount int
	err := ps.db.QueryRow(query, args...).Scan(&updatedCount)
	if err != nil {
		return 0, fmt.Errorf("failed to mark messages as read: %v", err)
	}

	return updatedCount, nil
}

// CleanupExpiredSessions removes expired sessions
func (ps *PostgresStore) CleanupExpiredSessions() (int, error) {
	var deletedCount int
	err := ps.db.QueryRow(`SELECT cleanup_expired_sessions()`).Scan(&deletedCount)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup expired sessions: %v", err)
	}

	log.Printf("Cleaned up %d expired sessions", deletedCount)
	return deletedCount, nil
}

// GetDatabaseStats returns database performance statistics
func (ps *PostgresStore) GetDatabaseStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get connection pool stats
	dbStats := ps.db.Stats()
	stats["max_open_connections"] = dbStats.MaxOpenConnections
	stats["open_connections"] = dbStats.OpenConnections
	stats["in_use"] = dbStats.InUse
	stats["idle"] = dbStats.Idle

	// Get table sizes
	var messagesCount, usersCount, sessionsCount int64

	err := ps.db.QueryRow("SELECT COUNT(*) FROM messages").Scan(&messagesCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get messages count: %v", err)
	}

	err = ps.db.QueryRow("SELECT COUNT(*) FROM users WHERE is_active = TRUE").Scan(&usersCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get users count: %v", err)
	}

	err = ps.db.QueryRow("SELECT COUNT(*) FROM sessions WHERE is_valid = TRUE").Scan(&sessionsCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get sessions count: %v", err)
	}

	stats["messages_count"] = messagesCount
	stats["active_users_count"] = usersCount
	stats["active_sessions_count"] = sessionsCount

	return stats, nil
}
