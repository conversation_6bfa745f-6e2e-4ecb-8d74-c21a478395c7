package storage

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// SQLiteStore handles message persistence with SQLite
type SQLiteStore struct {
	db *sql.DB
}

// NewSQLiteStore creates a new SQLite-based message store
func NewSQLiteStore(dbPath string) (*SQLiteStore, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	// Enable WAL mode for better concurrency
	if _, err := db.Exec("PRAGMA journal_mode=WAL"); err != nil {
		log.Printf("Warning: Failed to enable WAL mode: %v", err)
	}

	// Enable foreign keys
	if _, err := db.Exec("PRAGMA foreign_keys=ON"); err != nil {
		log.Printf("Warning: Failed to enable foreign keys: %v", err)
	}

	store := &SQLiteStore{db: db}

	// Initialize tables
	if err := store.initTables(); err != nil {
		return nil, fmt.Errorf("failed to initialize tables: %v", err)
	}

	log.Println("Successfully connected to SQLite database")
	return store, nil
}

// initTables creates the necessary tables if they don't exist
func (s *SQLiteStore) initTables() error {
	// Create messages table
	createMessagesTable := `
	CREATE TABLE IF NOT EXISTS messages (
		id TEXT PRIMARY KEY,
		sender_id TEXT NOT NULL,
		recipient_id TEXT,
		group_id TEXT,
		content BLOB NOT NULL,
		message_type TEXT NOT NULL DEFAULT 'chat',
		timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
		delivered BOOLEAN DEFAULT FALSE,
		read BOOLEAN DEFAULT FALSE
	);`

	if _, err := s.db.Exec(createMessagesTable); err != nil {
		return fmt.Errorf("failed to create messages table: %v", err)
	}

	// Create indexes for better performance
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_sender ON messages(sender_id)",
		"CREATE INDEX IF NOT EXISTS idx_recipient ON messages(recipient_id)",
		"CREATE INDEX IF NOT EXISTS idx_group ON messages(group_id)",
		"CREATE INDEX IF NOT EXISTS idx_timestamp ON messages(timestamp)",
	}

	for _, index := range indexes {
		if _, err := s.db.Exec(index); err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	return nil
}

// Close closes the database connection
func (s *SQLiteStore) Close() error {
	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// StoreMessage stores a new message
func (s *SQLiteStore) StoreMessage(msg Message) error {
	query := `
	INSERT INTO messages (
		id, sender_id, recipient_id, content, message_type,
		group_id, timestamp, delivered, read
	) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	id := uuid.New().String()
	_, err := s.db.Exec(query,
		id,
		msg.SenderId,
		msg.RecipientId,
		msg.Content,
		msg.Type,
		msg.GroupId,
		msg.Timestamp,
		msg.Delivered,
		msg.Read,
	)
	return err
}

// SaveMessage saves a message (alias for StoreMessage)
func (s *SQLiteStore) SaveMessage(msg Message) error {
	return s.StoreMessage(msg)
}

// GetUndeliveredMessages retrieves undelivered messages for a recipient
func (s *SQLiteStore) GetUndeliveredMessages(recipientId string) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE recipient_id = ? AND delivered = FALSE
	ORDER BY timestamp ASC
	`

	rows, err := s.db.Query(query, recipientId)
	if err != nil {
		return nil, fmt.Errorf("failed to query undelivered messages: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id, &msg.SenderId, &msg.RecipientId, &msg.Content,
			&msg.Type, &msg.GroupId, &msg.Timestamp,
			&msg.Delivered, &msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// MarkMessageDelivered marks a message as delivered
func (s *SQLiteStore) MarkMessageDelivered(messageId string) error {
	query := "UPDATE messages SET delivered = TRUE WHERE id = ?"
	_, err := s.db.Exec(query, messageId)
	return err
}

// MarkMessageRead marks a message as read
func (s *SQLiteStore) MarkMessageRead(messageId string) error {
	query := "UPDATE messages SET read = TRUE WHERE id = ?"
	_, err := s.db.Exec(query, messageId)
	return err
}

// GetGroupMessages retrieves messages for a group
func (s *SQLiteStore) GetGroupMessages(groupId string, limit int, offset int) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE group_id = ?
	ORDER BY timestamp DESC
	LIMIT ? OFFSET ?
	`

	rows, err := s.db.Query(query, groupId, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query group messages: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id, &msg.SenderId, &msg.RecipientId, &msg.Content,
			&msg.Type, &msg.GroupId, &msg.Timestamp,
			&msg.Delivered, &msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// GetMessageHistory retrieves chat history for a user
func (s *SQLiteStore) GetMessageHistory(userId string, limit int) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE sender_id = ? OR recipient_id = ?
	ORDER BY timestamp DESC
	LIMIT ?
	`

	rows, err := s.db.Query(query, userId, userId, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query message history: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id, &msg.SenderId, &msg.RecipientId, &msg.Content,
			&msg.Type, &msg.GroupId, &msg.Timestamp,
			&msg.Delivered, &msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// GetConversation retrieves conversation between two users
func (s *SQLiteStore) GetConversation(user1Id, user2Id string, limit, offset int) ([]*Message, error) {
	query := `
	SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
	FROM messages
	WHERE (sender_id = ? AND recipient_id = ?) OR (sender_id = ? AND recipient_id = ?)
	ORDER BY timestamp DESC
	LIMIT ? OFFSET ?
	`

	rows, err := s.db.Query(query, user1Id, user2Id, user2Id, user1Id, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query conversation: %v", err)
	}
	defer rows.Close()

	var messages []*Message
	for rows.Next() {
		msg := &Message{}
		var id string
		err := rows.Scan(
			&id, &msg.SenderId, &msg.RecipientId, &msg.Content,
			&msg.Type, &msg.GroupId, &msg.Timestamp,
			&msg.Delivered, &msg.Read,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan message: %v", err)
		}
		msg.ID = id
		messages = append(messages, msg)
	}

	return messages, nil
}

// MarkMessagesAsRead marks messages as read
func (s *SQLiteStore) MarkMessagesAsRead(recipientId, senderId string) (int, error) {
	query := "UPDATE messages SET read = TRUE WHERE recipient_id = ? AND sender_id = ? AND read = FALSE"
	result, err := s.db.Exec(query, recipientId, senderId)
	if err != nil {
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, err
	}

	return int(rowsAffected), nil
}

// CleanupExpiredSessions removes expired sessions (placeholder for SQLite)
func (s *SQLiteStore) CleanupExpiredSessions() (int, error) {
	// SQLite version doesn't have sessions table, return 0
	return 0, nil
}

// GetDatabaseStats returns database statistics
func (s *SQLiteStore) GetDatabaseStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get message count
	var messageCount int
	err := s.db.QueryRow("SELECT COUNT(*) FROM messages").Scan(&messageCount)
	if err != nil {
		return nil, err
	}
	stats["message_count"] = messageCount

	// Get database size (approximate)
	var pageCount, pageSize int
	err = s.db.QueryRow("PRAGMA page_count").Scan(&pageCount)
	if err == nil {
		err = s.db.QueryRow("PRAGMA page_size").Scan(&pageSize)
		if err == nil {
			stats["database_size_bytes"] = pageCount * pageSize
		}
	}

	return stats, nil
}
