package main

import (
	"log"
	"net/http"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		origin := r.Header.Get("Origin")
		log.Printf("WebSocket connection attempt from origin: %s", origin)
		// Allow connections from localhost:3000
		return origin == "http://localhost:3000" || origin == ""
	},
	EnableCompression: true,
}

func wsHandler(w http.ResponseWriter, r *http.Request) {
	userId := r.URL.Query().Get("userId")
	if userId == "" {
		log.Printf("WebSocket connection rejected: missing userId parameter")
		http.Error(w, "Missing userId parameter", http.StatusBadRequest)
		return
	}

	log.Printf("WebSocket connection request for userId: %s", userId)

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	log.Printf("WebSocket connected for userId: %s", userId)

	// Send welcome message
	welcomeMsg := map[string]interface{}{
		"type": "welcome",
		"message": "Connected to test WebSocket server",
		"userId": userId,
	}
	
	if err := conn.WriteJSON(welcomeMsg); err != nil {
		log.Printf("Failed to send welcome message: %v", err)
		return
	}

	// Handle WebSocket messages
	for {
		messageType, p, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Read error: %v", err)
			return
		}
		
		log.Printf("Received message from %s: %s", userId, string(p))
		
		// Echo the message back
		if err := conn.WriteMessage(messageType, p); err != nil {
			log.Printf("Write error: %v", err)
			return
		}
	}
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"ok","message":"Test WebSocket server is running"}`))
}

func main() {
	http.HandleFunc("/ws", wsHandler)
	http.HandleFunc("/health", healthHandler)
	
	log.Println("Test WebSocket server starting on :8080")
	log.Println("Health check: http://localhost:8080/health")
	log.Println("WebSocket endpoint: ws://localhost:8080/ws?userId=test")
	
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
