#!/bin/bash

# Create test users for Cypher Chat
echo "Creating test users for Cypher Chat..."

# Test user data
USERS=(
    '{"username":"alice","password":"test123","public_key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef"}'
    '{"username":"bob","password":"test123","public_key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef"}'
    '{"username":"charlie","password":"test123","public_key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef"}'
    '{"username":"diana","password":"test123","public_key":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef"}'
)

# Backend URL
BACKEND_URL="http://localhost:8080"

# Create each user
for user_data in "${USERS[@]}"; do
    username=$(echo $user_data | jq -r '.username')
    echo "Creating user: $username"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$user_data" \
        "$BACKEND_URL/api/auth/register")
    
    if [[ $? -eq 0 ]]; then
        echo "✅ User $username created successfully"
    else
        echo "❌ Failed to create user $username"
    fi
    
    sleep 1
done

echo ""
echo "🎉 Test users created! You can now login with:"
echo "👤 alice / test123"
echo "👤 bob / test123" 
echo "👤 charlie / test123"
echo "👤 diana / test123"
echo ""
echo "Open multiple browser tabs at http://localhost:3000 to test chat between users!"
