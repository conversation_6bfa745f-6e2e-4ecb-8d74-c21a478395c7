# Cypher Chat Database Schema

## Overview

The Cypher Chat application uses PostgreSQL for data storage. The database schema includes tables for users, sessions, messages, groups, and group members.

## Entity Relationship Diagram

```mermaid
erDiagram
    USERS {
        uuid id PK
        string username
        string password_hash
        string public_key
        timestamp created_at
        timestamp updated_at
    }
    
    SESSIONS {
        uuid id PK
        uuid user_id FK
        string token
        string ip_address
        string user_agent
        timestamp created_at
        timestamp expires_at
    }
    
    MESSAGES {
        uuid id PK
        uuid sender_id FK
        uuid recipient_id FK
        uuid group_id FK
        blob content
        string message_type
        timestamp timestamp
        boolean delivered
        boolean read
    }
    
    GROUPS {
        uuid id PK
        string name
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    GROUP_MEMBERS {
        uuid id PK
        uuid group_id FK
        uuid user_id FK
        timestamp joined_at
        string role
    }
    
    USERS ||--o{ SESSIONS : "has"
    USERS ||--o{ MESSAGES : "sends"
    USERS ||--o{ GROUPS : "creates"
    USERS ||--o{ GROUP_MEMBERS : "belongs to"
    GROUPS ||--o{ GROUP_MEMBERS : "has"
    GROUPS ||--o{ MESSAGES : "contains"
```

## Tables

### Users

Stores user account information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| username | VARCHAR(255) | Unique username |
| password_hash | VARCHAR(255) | Bcrypt hashed password |
| public_key | TEXT | User's public key for E2E encryption |
| created_at | TIMESTAMP | Account creation time |
| updated_at | TIMESTAMP | Account last update time |

### Sessions

Tracks user login sessions.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users table |
| token | VARCHAR(255) | JWT token |
| ip_address | VARCHAR(45) | Client IP address |
| user_agent | TEXT | Client user agent |
| created_at | TIMESTAMP | Session creation time |
| expires_at | TIMESTAMP | Session expiration time |

### Messages

Stores encrypted messages.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| sender_id | UUID | Foreign key to users table |
| recipient_id | UUID | Foreign key to users table |
| group_id | UUID | Foreign key to groups table (nullable) |
| content | BYTEA | Encrypted message content |
| message_type | VARCHAR(50) | Type of message (text, file, etc.) |
| timestamp | TIMESTAMP | Message creation time |
| delivered | BOOLEAN | Whether message was delivered |
| read | BOOLEAN | Whether message was read |

### Groups

Stores chat groups.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | VARCHAR(255) | Group name |
| created_by | UUID | Foreign key to users table |
| created_at | TIMESTAMP | Group creation time |
| updated_at | TIMESTAMP | Group last update time |

### Group Members

Tracks group membership.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups table |
| user_id | UUID | Foreign key to users table |
| joined_at | TIMESTAMP | When user joined the group |
| role | VARCHAR(50) | User role in group (admin, member) |

## Indexes

- `users_username_idx`: Index on `users.username` for fast lookups
- `sessions_token_idx`: Index on `sessions.token` for fast authentication
- `messages_recipient_idx`: Index on `messages.recipient_id` for fast message retrieval
- `messages_group_idx`: Index on `messages.group_id` for fast group message retrieval
- `group_members_user_idx`: Index on `group_members.user_id` for fast user group lookup

## Constraints

- `users_username_unique`: Unique constraint on `users.username`
- `sessions_token_unique`: Unique constraint on `sessions.token`
- `group_members_unique`: Unique constraint on `(group_id, user_id)` combination
