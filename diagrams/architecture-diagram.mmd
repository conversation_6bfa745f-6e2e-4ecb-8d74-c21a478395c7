graph TB
    subgraph "Frontend (React + TypeScript)"
        A[User Interface] --> B[Authentication Context]
        A --> C[WebSocket Hook]
        A --> D[Crypto Utils]
        B --> E[JWT Token Management]
        C --> F[Message Handling]
        D --> G[RSA + AES Encryption]
        F --> H[Chat Components]
    end
    
    subgraph "Backend (Go)"
        I[HTTP Server] --> J[Authentication Service]
        I --> K[WebSocket Server]
        J --> L[JWT Validation]
        K --> M[Client Management]
        K --> N[Message Router]
        M --> O[Public Key Exchange]
        N --> P[Message Storage]
    end
    
    subgraph "Database (PostgreSQL)"
        Q[Users Table]
        R[Messages Table]
        S[Sessions Table]
        T[Groups Table]
    end
    
    subgraph "Security Layer"
        U[End-to-End Encryption]
        V[Password Hashing]
        W[JWT Authentication]
    end
    
    C -.->|WebSocket Connection| K
    F -.->|Encrypted Messages| N
    J --> Q
    P --> R
    L --> S
    B -.->|Auth Requests| J
    G -.->|Client-side Encryption| U
    J -.->|bcrypt| V
    E -.->|Token Validation| W
    
    style A fill:#e1f5fe
    style I fill:#f3e5f5
    style Q fill:#e8f5e8
    style U fill:#fff3e0
