# Cypher Chat - Architecture Overview

## 🏗️ System Architecture

Cypher Chat is a sophisticated real-time messaging system built with a **React frontend**, **Go backend**, and **PostgreSQL database**. It implements **end-to-end encryption** using hybrid cryptography (RSA + AES) and real-time communication via WebSockets.

## 🔧 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Web Crypto API** for encryption
- **WebSocket** for real-time communication
- **Context API** for state management

### Backend
- **Go 1.21+** with Gorilla WebSocket
- **PostgreSQL** for data persistence
- **JWT** for authentication
- **bcrypt** for password hashing
- **Rate limiting** for security

### Infrastructure
- **Docker Compose** for containerization
- **CORS** enabled for cross-origin requests
- **TLS/HTTPS** ready for production

## 🏛️ Architecture Patterns

### 1. **Microservices-Ready Design**
- Modular backend with clear separation of concerns
- Database abstraction layer for easy switching
- API-first approach with RESTful endpoints

### 2. **Event-Driven Architecture**
- WebSocket-based real-time communication
- Channel-based message routing in Go
- Reactive frontend with hooks and contexts

### 3. **Security-First Design**
- End-to-end encryption by default
- Zero-knowledge server architecture
- Defense in depth with multiple security layers

## 🔄 Core Components

### Frontend Components
```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts for state
├── hooks/              # Custom React hooks
├── pages/              # Main application pages
├── services/           # API and business logic
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

### Backend Components
```
backend/
├── cmd/                # Application entry point
├── pkg/
│   ├── api/           # HTTP handlers and routing
│   ├── auth/          # Authentication service
│   ├── server/        # WebSocket server
│   ├── storage/       # Database layer
│   └── crypto/        # Cryptographic utilities
└── db/                # Database migrations
```

## 🔐 Security Architecture

### 1. **Authentication Layer**
- JWT-based stateless authentication
- Secure password hashing with bcrypt
- Token refresh mechanism
- Session management

### 2. **Encryption Layer**
- **Client-side encryption**: All encryption happens in browser
- **Hybrid cryptography**: RSA-2048 + AES-256-GCM
- **Perfect forward secrecy**: Unique keys per message
- **Zero-knowledge server**: Server never sees plaintext

### 3. **Network Security**
- WebSocket over TLS in production
- CORS protection
- Rate limiting and DDoS protection
- Input validation and sanitization

## 📊 Data Flow

### 1. **User Registration**
```
Browser → Generate RSA Keys → Send Public Key → Backend → Store in DB
```

### 2. **Message Sending**
```
Compose → Encrypt (AES+RSA) → WebSocket → Backend → Route → Recipient
```

### 3. **Message Receiving**
```
WebSocket → Frontend → Decrypt (RSA+AES) → Display
```

## 🚀 Performance Considerations

### Frontend Optimizations
- Component memoization with React.memo
- Virtual scrolling for message lists
- Debounced typing indicators
- Local message caching

### Backend Optimizations
- Goroutine-based concurrent processing
- Connection pooling for database
- Efficient message routing
- Memory-efficient client management

### Database Optimizations
- Indexed queries for message retrieval
- Optimized schema design
- Connection pooling
- Prepared statements

## 🔧 Deployment Architecture

### Development
```
Frontend (Vite) → Backend (Go) → PostgreSQL (Docker)
```

### Production
```
Frontend (CDN) → Load Balancer → Backend Cluster → PostgreSQL Cluster
```

## 📈 Scalability Design

### Horizontal Scaling
- Stateless backend design
- Database connection pooling
- Load balancer ready
- Session clustering support

### Vertical Scaling
- Efficient memory usage
- Optimized database queries
- Minimal resource footprint
- Configurable limits

## 🛡️ Security Measures

### Application Security
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection

### Infrastructure Security
- TLS encryption in transit
- Secure headers
- Rate limiting
- DDoS protection

### Data Security
- End-to-end encryption
- Secure key management
- Password hashing
- Audit logging

## 🔍 Monitoring & Observability

### Logging
- Structured logging with levels
- Request/response logging
- Error tracking
- Performance metrics

### Health Checks
- Database connectivity
- WebSocket status
- Memory usage
- Connection counts

## 🎯 Key Design Decisions

### Why Go for Backend?
- Excellent concurrency with goroutines
- Fast compilation and execution
- Strong standard library
- Memory safety with garbage collection

### Why React for Frontend?
- Component-based architecture
- Strong TypeScript support
- Rich ecosystem
- Excellent developer experience

### Why PostgreSQL?
- ACID compliance for reliable messaging
- JSON support for flexible data
- Excellent performance
- Strong consistency guarantees

### Why WebSockets?
- Real-time bidirectional communication
- Lower latency than HTTP polling
- Efficient for chat applications
- Native browser support

## 🔮 Future Enhancements

### Planned Features
- Group chat support
- File sharing with encryption
- Voice/video calling
- Mobile applications
- Federation support

### Technical Improvements
- Redis for session storage
- Kubernetes deployment
- Microservices architecture
- Advanced monitoring
- Auto-scaling capabilities

This architecture provides a solid foundation for a secure, scalable, and maintainable chat application while prioritizing user privacy and security.
