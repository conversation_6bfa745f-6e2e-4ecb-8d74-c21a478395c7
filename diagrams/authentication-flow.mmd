sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant DB as Database
    participant C as Crypto API
    
    Note over U,C: User Registration
    U->>F: Enter username/password
    F->>C: Generate RSA key pair
    C->>F: Return public/private keys
    F->>F: Store private key locally
    F->>B: POST /register {username, password, publicKey}
    B->>B: Hash password with bcrypt
    B->>DB: Store user with hashed password
    DB->>B: User created
    B->>B: Generate JWT token
    B->>F: Return {user, token}
    F->>F: Store token in localStorage
    
    Note over U,C: User Login
    U->>F: Enter username/password
    F->>B: POST /login {username, password}
    B->>DB: Get user by username
    DB->>B: Return user data
    B->>B: Verify password with bcrypt
    B->>B: Generate JWT token
    B->>DB: Create session record
    B->>F: Return {user, token}
    F->>F: Store token in localStorage
    F->>C: Retrieve stored private key
    C->>F: Return private key
    
    Note over U,C: WebSocket Connection
    F->>B: Connect WebSocket with userId
    B->>B: Validate JWT token
    B->>B: Create client instance
    F->>B: Send public key message
    B->>B: Store client public key
    B->>F: Broadcast client list
    
    Note over U,C: Token Refresh
    loop Every 30 minutes
        F->>B: POST /refresh-token
        B->>B: Validate current token
        B->>B: Generate new token
        B->>DB: Update session
        B->>F: Return new token
        F->>F: Update stored token
    end
    
    Note over U,C: Logout
    U->>F: Click logout
    F->>B: POST /logout
    B->>DB: Invalidate session
    F->>F: Clear localStorage
    F->>B: Close WebSocket connection
