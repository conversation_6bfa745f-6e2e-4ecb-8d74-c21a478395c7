# Cypher Chat - Backend Analysis

## 🏗️ Go Backend Architecture

The Cypher Chat backend is built with Go, leveraging its excellent concurrency model and performance characteristics for real-time messaging applications.

## 📁 Project Structure

```
backend/
├── cmd/
│   └── main.go              # Application entry point
├── pkg/
│   ├── api/                 # HTTP API layer
│   │   ├── router.go        # Route definitions
│   │   ├── auth_handler.go  # Authentication endpoints
│   │   └── middleware.go    # HTTP middleware
│   ├── auth/                # Authentication service
│   │   ├── auth.go          # Core auth logic
│   │   ├── jwt.go           # JWT token handling
│   │   └── types.go         # Auth types
│   ├── server/              # WebSocket server
│   │   ├── server.go        # Main server logic
│   │   ├── client.go        # Client management
│   │   ├── message.go       # Message handling
│   │   └── handlers.go      # WebSocket handlers
│   ├── storage/             # Database layer
│   │   ├── postgres.go      # PostgreSQL implementation
│   │   ├── types.go         # Database types
│   │   └── migrations.go    # Schema migrations
│   └── crypto/              # Cryptographic utilities
└── db/                      # Database files
    └── init/                # Initialization scripts
```

## 🔧 Core Components

### 1. **Main Application (cmd/main.go)**

```go
func main() {
    // Load configuration
    config := loadConfig()
    
    // Initialize database
    db, err := sql.Open("postgres", config.DatabaseURL)
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }
    defer db.Close()
    
    // Create storage layer
    store := storage.NewPostgresStore(db)
    
    // Create authentication service
    authService := auth.NewAuthService(store, config.JWTSecret)
    
    // Create chat server
    chatServer := server.NewServer(store)
    
    // Create API router
    router := api.NewRouter(authService, chatServer)
    
    // Start server
    log.Printf("Starting HTTP server on :%s", config.Port)
    if err := router.Start(":" + config.Port); err != nil {
        log.Fatalf("Failed to start server: %v", err)
    }
}
```

### 2. **WebSocket Server (pkg/server/)**

#### Server Structure
```go
type Server struct {
    clients       sync.Map        // Thread-safe client storage
    sessions      sync.Map        // Session management
    store         storage.Store   // Database interface
    register      chan *Client    // Client registration channel
    unregister    chan *Client    // Client cleanup channel
    broadcast     chan []byte     // Message broadcasting
    rateLimiter   *ratelimit.RateLimiter
    maxClients    int
    clientCount   int64
    clientCountMu sync.RWMutex
}
```

#### Client Management
```go
type Client struct {
    ID        string          `json:"id"`
    PublicKey string          `json:"public_key"`
    Session   *Session        `json:"-"`
    Conn      *websocket.Conn `json:"-"`
    send      chan []byte     `json:"-"`
    Server    *Server         `json:"-"`
}

func (s *Server) handleClientRegistration(client *Client) {
    // Check for existing client and replace if necessary
    if existingClient, ok := s.clients.Load(client.ID); ok {
        existing := existingClient.(*Client)
        existing.Conn.Close()
        s.clients.Delete(client.ID)
    } else {
        // Increment client count for new connections
        s.clientCountMu.Lock()
        s.clientCount++
        currentCount := s.clientCount
        s.clientCountMu.Unlock()
        log.Printf("Client count increased to: %d", currentCount)
    }
    
    // Store new client
    s.clients.Store(client.ID, client)
    log.Printf("Client registered: %s", client.ID)
    
    // Broadcast updated client list
    go s.broadcastClientList()
}
```

### 3. **Message Processing Pipeline**

#### Message Types
```go
const (
    MessageTypePublicKey       = "public_key"
    MessageTypeChat            = "chat"
    MessageTypeClientList      = "client_list"
    MessageTypeTyping          = "typing"
    MessageTypeDeliveryReceipt = "delivery_receipt"
)
```

#### Message Handler
```go
func (c *Client) handleWebSocketMessage(messageType int, payload []byte) {
    var msg Message
    if err := json.Unmarshal(payload, &msg); err != nil {
        log.Printf("Failed to parse message: %v", err)
        return
    }
    
    switch msg.Type {
    case MessageTypePublicKey:
        c.handlePublicKey(msg)
    case MessageTypeChat:
        c.handleChatMessage(msg)
    case MessageTypeTyping:
        c.handleTypingMessage(msg)
    case MessageTypeDeliveryReceipt:
        c.handleDeliveryReceipt(msg)
    default:
        log.Printf("Unknown message type: %s", msg.Type)
    }
}
```

#### Chat Message Processing
```go
func (c *Client) handleChatMessage(msg Message) {
    // Validate message
    if err := validateChatMessage(msg); err != nil {
        log.Printf("Invalid chat message: %v", err)
        return
    }
    
    // Store message in database
    dbMsg := storage.Message{
        SenderId:    c.ID,
        RecipientId: msg.RecipientId,
        Content:     []byte(msg.Content),
        Type:        msg.Type,
        Timestamp:   time.Now(),
        Delivered:   false,
        Read:        false,
    }
    
    if err := c.Server.store.StoreMessage(dbMsg); err != nil {
        log.Printf("Failed to store message: %v", err)
        return
    }
    
    // Forward to recipient
    if recipientClient, ok := c.Server.clients.Load(msg.RecipientId); ok {
        recipient := recipientClient.(*Client)
        
        // Prepare message for forwarding
        forwardMsg := Message{
            Type:        MessageTypeChat,
            Content:     msg.Content,
            SenderId:    c.ID,
            RecipientId: msg.RecipientId,
            Timestamp:   time.Now(),
        }
        
        if data, err := json.Marshal(forwardMsg); err == nil {
            select {
            case recipient.send <- data:
                // Message sent successfully
                log.Printf("Message forwarded from %s to %s", c.ID, msg.RecipientId)
            default:
                // Recipient's send buffer is full
                log.Printf("Failed to send message to %s: buffer full", msg.RecipientId)
            }
        }
    } else {
        log.Printf("Recipient %s not found", msg.RecipientId)
    }
}
```

### 4. **Authentication Service (pkg/auth/)**

#### User Management
```go
type AuthService struct {
    store     storage.Store
    jwtSecret []byte
    tokenExp  time.Duration
}

func (a *AuthService) Register(username, password, publicKey string) (*User, error) {
    // Check if user already exists
    _, err := a.store.GetUserByUsername(username)
    if err == nil {
        return nil, ErrUserExists
    }
    
    // Create new user
    user := &User{
        ID:        uuid.New().String(),
        Username:  username,
        PublicKey: publicKey,
        CreatedAt: time.Now(),
        Status:    "offline",
        IsActive:  true,
    }
    
    // Store user with hashed password
    if err := a.store.CreateUser(user, password); err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }
    
    return user, nil
}
```

#### JWT Token Management
```go
func (a *AuthService) generateToken(user *User) (string, error) {
    claims := &Claims{
        UserID:   user.ID,
        Username: user.Username,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(a.tokenExp)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
            NotBefore: jwt.NewNumericDate(time.Now()),
            Issuer:    "cypher-chat",
            Subject:   user.ID,
            ID:        uuid.New().String(),
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString(a.jwtSecret)
}

func (a *AuthService) VerifyToken(tokenString string) (*User, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return a.jwtSecret, nil
    })
    
    if err != nil {
        return nil, ErrInvalidToken
    }
    
    if !token.Valid {
        return nil, ErrInvalidToken
    }
    
    claims, ok := token.Claims.(*Claims)
    if !ok {
        return nil, ErrInvalidToken
    }
    
    if claims.ExpiresAt.Time.Before(time.Now()) {
        return nil, ErrExpiredToken
    }
    
    user, err := a.store.GetUserByID(claims.UserID)
    if err != nil {
        return nil, ErrUserNotFound
    }
    
    return user, nil
}
```

### 5. **Database Layer (pkg/storage/)**

#### PostgreSQL Implementation
```go
type PostgresStore struct {
    db *sql.DB
}

func (ps *PostgresStore) StoreMessage(msg Message) error {
    query := `
    INSERT INTO messages (
        id, sender_id, recipient_id, content, message_type,
        group_id, timestamp, delivered, read
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `
    
    _, err := ps.db.Exec(query,
        uuid.New(),
        msg.SenderId,
        msg.RecipientId,
        msg.Content,
        msg.Type,
        msg.GroupId,
        msg.Timestamp,
        msg.Delivered,
        msg.Read,
    )
    return err
}

func (ps *PostgresStore) GetMessageHistory(userId string, limit int) ([]*Message, error) {
    query := `
    (SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
     FROM messages
     WHERE sender_id = $1
     ORDER BY timestamp DESC
     LIMIT $2)
    UNION ALL
    (SELECT id, sender_id, recipient_id, content, message_type, group_id, timestamp, delivered, read
     FROM messages
     WHERE recipient_id = $1 AND sender_id != $1
     ORDER BY timestamp DESC
     LIMIT $2)
    ORDER BY timestamp DESC
    LIMIT $2
    `
    
    rows, err := ps.db.Query(query, userId, limit)
    if err != nil {
        return nil, fmt.Errorf("failed to query message history: %v", err)
    }
    defer rows.Close()
    
    var messages []*Message
    for rows.Next() {
        msg := &Message{}
        var id string
        err := rows.Scan(
            &id, &msg.SenderId, &msg.RecipientId, &msg.Content,
            &msg.Type, &msg.GroupId, &msg.Timestamp,
            &msg.Delivered, &msg.Read,
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan message: %v", err)
        }
        
        msg.ID = id
        messages = append(messages, msg)
    }
    
    return messages, nil
}
```

## 🔄 Concurrency Model

### Goroutine Usage
```go
// WebSocket connection handling
func (s *Server) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
    // ... connection setup ...
    
    // Start goroutines for each client
    go client.readPump()   // Handle incoming messages
    go client.writePump()  // Handle outgoing messages
}

// Message processing
func (c *Client) readPump() {
    defer func() {
        c.Server.unregister <- c
        c.Conn.Close()
    }()
    
    for {
        _, message, err := c.Conn.ReadMessage()
        if err != nil {
            break
        }
        
        // Process message in separate goroutine
        go func(msg []byte) {
            c.handleWebSocketMessage(websocket.TextMessage, msg)
        }(message)
    }
}
```

### Channel-Based Communication
```go
// Server event loop
func (s *Server) run() {
    for {
        select {
        case client := <-s.register:
            s.handleClientRegistration(client)
            
        case client := <-s.unregister:
            s.handleClientUnregistration(client)
            
        case message := <-s.broadcast:
            s.handleBroadcast(message)
        }
    }
}
```

## 🛡️ Security Features

### Rate Limiting
```go
type RateLimiter struct {
    connections map[string]*time.Timer
    messages    map[string]*MessageLimiter
    mutex       sync.RWMutex
}

func (rl *RateLimiter) Allow(clientIP, action string) error {
    rl.mutex.Lock()
    defer rl.mutex.Unlock()
    
    switch action {
    case "connection":
        if timer, exists := rl.connections[clientIP]; exists {
            if !timer.Stop() {
                return errors.New("connection rate limit exceeded")
            }
        }
        
        rl.connections[clientIP] = time.AfterFunc(time.Minute, func() {
            rl.mutex.Lock()
            delete(rl.connections, clientIP)
            rl.mutex.Unlock()
        })
        
    case "message":
        // Implement message rate limiting
    }
    
    return nil
}
```

### Input Validation
```go
func validateChatMessage(msg Message) error {
    if len(msg.Content) > maxMessageSize {
        return errors.New("message too large")
    }
    
    if msg.RecipientId == "" {
        return errors.New("recipient ID required")
    }
    
    if !isValidUUID(msg.RecipientId) {
        return errors.New("invalid recipient ID format")
    }
    
    return nil
}
```

## 📊 Performance Optimizations

### Connection Pooling
```go
func NewPostgresStore(databaseURL string) (*PostgresStore, error) {
    db, err := sql.Open("postgres", databaseURL)
    if err != nil {
        return nil, err
    }
    
    // Configure connection pool
    db.SetMaxOpenConns(25)
    db.SetMaxIdleConns(5)
    db.SetConnMaxLifetime(5 * time.Minute)
    
    return &PostgresStore{db: db}, nil
}
```

### Memory Management
```go
// Efficient client cleanup
func (s *Server) handleClientUnregistration(client *Client) {
    if _, ok := s.clients.Load(client.ID); ok {
        s.clients.Delete(client.ID)
        close(client.send)
        
        s.clientCountMu.Lock()
        s.clientCount--
        currentCount := s.clientCount
        s.clientCountMu.Unlock()
        
        log.Printf("Client unregistered: %s (count: %d)", client.ID, currentCount)
        go s.broadcastClientList()
    }
}
```

This backend architecture provides a robust, scalable, and secure foundation for real-time messaging with excellent performance characteristics.
