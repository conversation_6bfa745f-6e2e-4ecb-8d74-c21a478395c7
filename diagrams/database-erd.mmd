erDiagram
    USERS {
        uuid id PK
        string username
        string password_hash
        string public_key
        timestamp created_at
        timestamp updated_at
        string status
        boolean is_active
        timestamp last_seen
        string profile_picture_url
        string display_name
    }
    
    SESSIONS {
        uuid id PK
        uuid user_id FK
        string token
        inet ip_address
        string user_agent
        timestamp created_at
        timestamp expires_at
        boolean is_active
        timestamp last_activity
    }
    
    MESSAGES {
        uuid id PK
        uuid sender_id FK
        uuid recipient_id FK
        uuid group_id FK
        bytea content
        string message_type
        timestamp timestamp
        boolean delivered
        boolean read
        timestamp read_at
        timestamp edited_at
        uuid reply_to_id FK
        jsonb metadata
    }
    
    GROUPS {
        uuid id PK
        string name
        string description
        uuid created_by FK
        timestamp created_at
        timestamp updated_at
        boolean is_active
        string group_type
        integer max_members
        jsonb settings
    }
    
    GROUP_MEMBERS {
        uuid id PK
        uuid group_id FK
        uuid user_id FK
        timestamp joined_at
        string role
        boolean is_active
        uuid invited_by FK
    }
    
    MESSAGE_ATTACHMENTS {
        uuid id PK
        uuid message_id FK
        string filename
        bigint file_size
        string mime_type
        string file_path
        bytea encryption_key
        timestamp created_at
    }
    
    USERS ||--o{ SESSIONS : "has"
    USERS ||--o{ MESSAGES : "sends"
    USERS ||--o{ MESSAGES : "receives"
    USERS ||--o{ GROUPS : "creates"
    USERS ||--o{ GROUP_MEMBERS : "belongs to"
    GROUPS ||--o{ GROUP_MEMBERS : "has"
    GROUPS ||--o{ MESSAGES : "contains"
    MESSAGES ||--o{ MESSAGE_ATTACHMENTS : "has"
    MESSAGES ||--o{ MESSAGES : "replies to"
