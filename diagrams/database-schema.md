# Cypher Chat - Database Schema

## 🗄️ PostgreSQL Database Design

The Cypher Chat application uses PostgreSQL as its primary database, designed for scalability, performance, and data integrity in a real-time messaging environment.

## 📊 Database Schema Overview

### Core Tables

#### 1. **Users Table**
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    public_key TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'offline',
    is_active BOOLEAN DEFAULT true,
    last_seen TIMESTAMP WITH TIME ZONE,
    profile_picture_url TEXT,
    display_name <PERSON><PERSON><PERSON><PERSON>(255)
);

-- Indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_last_seen ON users(last_seen);
```

#### 2. **Sessions Table**
```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(512) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for session management
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_token ON sessions(token);
CREATE INDEX idx_sessions_expires_at ON sessions(expires_at);
CREATE INDEX idx_sessions_active ON sessions(is_active, expires_at);
```

#### 3. **Messages Table**
```sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    group_id UUID REFERENCES groups(id) ON DELETE CASCADE,
    content BYTEA NOT NULL, -- Encrypted message content
    message_type VARCHAR(50) NOT NULL DEFAULT 'chat',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    delivered BOOLEAN DEFAULT false,
    read BOOLEAN DEFAULT false,
    read_at TIMESTAMP WITH TIME ZONE,
    edited_at TIMESTAMP WITH TIME ZONE,
    reply_to_id UUID REFERENCES messages(id),
    metadata JSONB, -- Additional message metadata
    
    -- Ensure either recipient_id or group_id is set
    CONSTRAINT check_recipient_or_group CHECK (
        (recipient_id IS NOT NULL AND group_id IS NULL) OR
        (recipient_id IS NULL AND group_id IS NOT NULL)
    )
);

-- Indexes for message retrieval optimization
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_recipient_id ON messages(recipient_id);
CREATE INDEX idx_messages_group_id ON messages(group_id);
CREATE INDEX idx_messages_timestamp ON messages(timestamp DESC);
CREATE INDEX idx_messages_type ON messages(message_type);

-- Composite indexes for common queries
CREATE INDEX idx_messages_sender_timestamp ON messages(sender_id, timestamp DESC);
CREATE INDEX idx_messages_recipient_timestamp ON messages(recipient_id, timestamp DESC);
CREATE INDEX idx_messages_group_timestamp ON messages(group_id, timestamp DESC);
CREATE INDEX idx_messages_conversation ON messages(sender_id, recipient_id, timestamp DESC);
```

#### 4. **Groups Table**
```sql
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    group_type VARCHAR(50) DEFAULT 'private', -- private, public, channel
    max_members INTEGER DEFAULT 100,
    settings JSONB DEFAULT '{}' -- Group-specific settings
);

-- Indexes for group management
CREATE INDEX idx_groups_created_by ON groups(created_by);
CREATE INDEX idx_groups_type ON groups(group_type);
CREATE INDEX idx_groups_active ON groups(is_active);
```

#### 5. **Group Members Table**
```sql
CREATE TABLE group_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    role VARCHAR(50) DEFAULT 'member', -- admin, moderator, member
    is_active BOOLEAN DEFAULT true,
    invited_by UUID REFERENCES users(id),
    
    -- Unique constraint to prevent duplicate memberships
    UNIQUE(group_id, user_id)
);

-- Indexes for group membership queries
CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_user_id ON group_members(user_id);
CREATE INDEX idx_group_members_role ON group_members(role);
CREATE INDEX idx_group_members_active ON group_members(is_active);
```

#### 6. **Message Attachments Table**
```sql
CREATE TABLE message_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL, -- Path to encrypted file
    encryption_key BYTEA NOT NULL, -- Encrypted file key
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for attachment queries
CREATE INDEX idx_attachments_message_id ON message_attachments(message_id);
CREATE INDEX idx_attachments_mime_type ON message_attachments(mime_type);
```

## 🔧 Database Functions and Triggers

### 1. **Update Timestamp Trigger**
```sql
-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at 
    BEFORE UPDATE ON groups 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 2. **Message History Function**
```sql
-- Optimized function to get conversation history
CREATE OR REPLACE FUNCTION get_conversation_history(
    user1_id UUID,
    user2_id UUID,
    limit_count INTEGER DEFAULT 50,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    sender_id UUID,
    recipient_id UUID,
    content BYTEA,
    message_type VARCHAR,
    timestamp TIMESTAMP WITH TIME ZONE,
    delivered BOOLEAN,
    read BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.sender_id,
        m.recipient_id,
        m.content,
        m.message_type,
        m.timestamp,
        m.delivered,
        m.read
    FROM messages m
    WHERE 
        (m.sender_id = user1_id AND m.recipient_id = user2_id) OR
        (m.sender_id = user2_id AND m.recipient_id = user1_id)
    ORDER BY m.timestamp DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;
```

### 3. **User Activity Tracking**
```sql
-- Function to update user last seen
CREATE OR REPLACE FUNCTION update_user_activity(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE users 
    SET 
        last_seen = CURRENT_TIMESTAMP,
        status = 'online'
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql;
```

## 📈 Performance Optimizations

### 1. **Partitioning Strategy**
```sql
-- Partition messages table by timestamp for better performance
CREATE TABLE messages_2024 PARTITION OF messages
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE messages_2025 PARTITION OF messages
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

### 2. **Materialized Views for Analytics**
```sql
-- Materialized view for user statistics
CREATE MATERIALIZED VIEW user_message_stats AS
SELECT 
    u.id,
    u.username,
    COUNT(m.id) as total_messages,
    COUNT(CASE WHEN m.timestamp > CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as messages_last_week,
    MAX(m.timestamp) as last_message_at
FROM users u
LEFT JOIN messages m ON u.id = m.sender_id
GROUP BY u.id, u.username;

-- Refresh strategy
CREATE INDEX ON user_message_stats (username);
```

### 3. **Connection Pooling Configuration**
```sql
-- Recommended PostgreSQL settings for chat application
-- postgresql.conf settings:
-- max_connections = 200
-- shared_buffers = 256MB
-- effective_cache_size = 1GB
-- work_mem = 4MB
-- maintenance_work_mem = 64MB
-- checkpoint_completion_target = 0.9
-- wal_buffers = 16MB
-- default_statistics_target = 100
```

## 🔒 Security Measures

### 1. **Row Level Security (RLS)**
```sql
-- Enable RLS on sensitive tables
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;

-- Policy for message access
CREATE POLICY message_access_policy ON messages
    FOR ALL
    TO authenticated_users
    USING (
        sender_id = current_user_id() OR 
        recipient_id = current_user_id()
    );

-- Policy for session access
CREATE POLICY session_access_policy ON sessions
    FOR ALL
    TO authenticated_users
    USING (user_id = current_user_id());
```

### 2. **Data Encryption at Rest**
```sql
-- Enable transparent data encryption
-- This is typically configured at the PostgreSQL cluster level
-- ALTER SYSTEM SET ssl = on;
-- ALTER SYSTEM SET ssl_cert_file = 'server.crt';
-- ALTER SYSTEM SET ssl_key_file = 'server.key';
```

### 3. **Audit Logging**
```sql
-- Create audit log table
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(255) NOT NULL,
    operation VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    user_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    old_values JSONB,
    new_values JSONB,
    ip_address INET
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, operation, old_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD));
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, operation, old_values, new_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, operation, new_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW));
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
```

## 🔄 Migration Strategy

### 1. **Version Control for Schema**
```sql
-- Schema version tracking
CREATE TABLE schema_migrations (
    version VARCHAR(255) PRIMARY KEY,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Example migration
-- V001__initial_schema.sql
-- V002__add_groups.sql
-- V003__add_attachments.sql
```

### 2. **Backup and Recovery**
```bash
# Automated backup script
#!/bin/bash
pg_dump -h localhost -U postgres -d cypher_chat \
    --format=custom \
    --compress=9 \
    --file="backup_$(date +%Y%m%d_%H%M%S).dump"

# Point-in-time recovery setup
# Enable WAL archiving in postgresql.conf:
# wal_level = replica
# archive_mode = on
# archive_command = 'cp %p /path/to/archive/%f'
```

This database schema provides a robust foundation for the Cypher Chat application with excellent performance, security, and scalability characteristics.
