flowchart TD
    subgraph "Key Generation (Registration)"
        A[User registers] --> B[Generate RSA-2048 key pair]
        B --> C[Export public key to PEM]
        C --> D[Store private key locally]
        D --> E[Send public key to server]
    end

    
    subgraph "Message Encryption (Sending)"
        F[User types message] --> G[Get recipient's public key]
        G --> H[Generate random AES-256 key]
        H --> I[Generate random 96-bit IV]
        I --> J[Encrypt message with AES-GCM]
        J --> K[Encrypt AES key with RSA-OAEP]
        K --> L[Combine: encrypted_AES_key + IV + encrypted_message]
        L --> M[Convert to base64]
        M --> N[Send via WebSocket]
    end
    
    subgraph "Message Decryption (Receiving)"
        O[Receive encrypted message] --> P[Convert from base64]
        P --> Q[Extract encrypted AES key]
        Q --> R[Extract IV]
        R --> S[Extract encrypted message]
        S --> T[Decrypt AES key with RSA private key]
        T --> U[Import decrypted AES key]
        U --> V[Decrypt message with AES-GCM]
        V --> W[Display plaintext message]
    end
    
    subgraph "Security Features"
        X[Perfect Forward Secrecy]
        Y[Zero-Knowledge Server]
        Z[Client-side Only Encryption]
    end
    
    E --> G
    N --> O
    H -.-> X
    D -.-> Y
    B -.-> Z
    
    style B fill:#fff3e0
    style H fill:#fff3e0
    style J fill:#ffebee
    style K fill:#ffebee
    style T fill:#e8f5e8
    style V fill:#e8f5e8
