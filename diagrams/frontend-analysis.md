# Cypher Chat - Frontend Analysis

## ⚛️ React Frontend Architecture

The Cypher Chat frontend is built with React 18 and TypeScript, implementing a modern, responsive, and secure chat interface with client-side encryption.

## 📁 Project Structure

```
frontend/src/
├── components/              # Reusable UI components
│   ├── ChatHeader.tsx       # Chat header with user info
│   ├── ChatMainArea.tsx     # Main chat interface
│   ├── ChatSidebar.tsx      # User list and navigation
│   ├── LoadingSpinner.tsx   # Loading indicators
│   ├── ProtectedRoute.tsx   # Route protection
│   ├── SettingsPanel.tsx    # User settings
│   └── WelcomeScreen.tsx    # Welcome/landing screen
├── contexts/                # React contexts for state management
│   ├── AuthContext.tsx      # Authentication state
│   └── AuthContextImproved.tsx # Enhanced auth context
├── hooks/                   # Custom React hooks
│   ├── useWebSocket.ts      # WebSocket connection
│   └── useWebSocketImproved.ts # Enhanced WebSocket hook
├── pages/                   # Main application pages
│   ├── ChatPage.tsx         # Main chat interface
│   ├── SignInPage.tsx       # Login/registration
│   └── HomePage.tsx         # Landing page
├── services/                # Business logic and API calls
│   ├── api.ts               # HTTP API client
│   ├── chatService.ts       # Chat-specific services
│   └── authService.ts       # Authentication services
├── types/                   # TypeScript type definitions
│   ├── auth.ts              # Authentication types
│   ├── chat.ts              # Chat-related types
│   └── messages.ts          # Message types
├── utils/                   # Utility functions
│   ├── crypto.ts            # Encryption/decryption
│   ├── logger.ts            # Logging utilities
│   └── storage.ts           # Local storage helpers
└── styles/                  # CSS and styling
    └── index.css            # Global styles
```

## 🔧 Core Components

### 1. **Authentication Context**

```typescript
interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication state
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        try {
          const userData = await verifyToken(token);
          setUser(userData);
        } catch (err) {
          localStorage.removeItem('token');
        }
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  const login = async (username: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await authService.login(username, password);
      localStorage.setItem('token', response.token);
      setUser(response.user);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading, error }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. **Enhanced WebSocket Hook**

```typescript
const useWebSocketImproved = (): UseWebSocketReturn => {
  const { user } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>("Disconnected");
  const [lastMessage, setLastMessage] = useState<WebSocketEventMap["message"] | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef(true);
  const isConnectingRef = useRef(false);

  // Connection configuration
  const CONNECTION_CONFIG = {
    maxReconnectAttempts: 10,
    initialReconnectDelay: 1000,
    maxReconnectDelay: 30000,
    heartbeatInterval: 30000,
    connectionTimeout: 10000,
  };

  // Setup heartbeat mechanism
  const setupHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: "ping" }));
      }
    }, CONNECTION_CONFIG.heartbeatInterval);
  }, []);

  // Handle connection success
  const handleConnectionSuccess = useCallback(() => {
    setConnectionStatus("Connected");
    setReconnectAttempts(0);
    isConnectingRef.current = false;
    logger.info("WebSocket connected successfully");

    // Send public key
    if (user?.exportedPublicKey && wsRef.current?.readyState === WebSocket.OPEN) {
      const publicKeyMessage = {
        type: "public_key",
        content: {
          key: user.exportedPublicKey,
        },
        timestamp: new Date().toISOString(),
      };
      wsRef.current.send(JSON.stringify(publicKeyMessage));
    }

    setupHeartbeat();
  }, [user, setupHeartbeat]);

  // Handle connection failure with exponential backoff
  const handleConnectionFailure = useCallback(() => {
    if (!shouldReconnectRef.current) return;

    const currentAttempts = reconnectAttempts;
    if (currentAttempts >= CONNECTION_CONFIG.maxReconnectAttempts) {
      setConnectionStatus("Error");
      logger.error("Max reconnection attempts reached");
      return;
    }

    setConnectionStatus("Reconnecting");
    setReconnectAttempts(prev => prev + 1);

    // Exponential backoff with jitter
    const baseDelay = Math.min(
      CONNECTION_CONFIG.initialReconnectDelay * Math.pow(2, currentAttempts),
      CONNECTION_CONFIG.maxReconnectDelay
    );
    const jitter = Math.random() * 1000;
    const delay = baseDelay + jitter;

    logger.info(`Reconnecting in ${delay.toFixed(1)}ms (attempt ${currentAttempts + 1})`);

    reconnectTimeoutRef.current = setTimeout(() => {
      if (shouldReconnectRef.current) {
        connect();
      }
    }, delay);
  }, [reconnectAttempts]);

  // Main connection function
  const connect = useCallback(() => {
    if (!user || isConnectingRef.current) return;

    isConnectingRef.current = true;
    setConnectionStatus("Connecting");

    try {
      const socket = new WebSocket(`${WS_URL}?userId=${user.id}`);
      wsRef.current = socket;

      // Connection timeout
      const connectionTimeout = setTimeout(() => {
        if (socket.readyState === WebSocket.CONNECTING) {
          socket.close();
          handleConnectionFailure();
        }
      }, CONNECTION_CONFIG.connectionTimeout);

      socket.onopen = () => {
        clearTimeout(connectionTimeout);
        handleConnectionSuccess();
      };

      socket.onmessage = (event) => {
        setLastMessage(event);
        logger.debug("WebSocket message received", { data: event.data });

        // Handle pong responses
        try {
          const message = JSON.parse(event.data);
          if (message.type === "pong") {
            return;
          }
        } catch (e) {
          // Not JSON, ignore
        }
      };

      socket.onclose = (event) => {
        clearTimeout(connectionTimeout);
        isConnectingRef.current = false;
        
        if (event.code === 1000) {
          logger.info("WebSocket closed normally");
          setConnectionStatus("Disconnected");
        } else {
          logger.warn("WebSocket closed abnormally", { code: event.code, reason: event.reason });
          handleConnectionFailure();
        }
      };

      socket.onerror = (error) => {
        clearTimeout(connectionTimeout);
        logger.error("WebSocket error", { error });
        isConnectingRef.current = false;
      };

    } catch (error) {
      logger.error("Failed to create WebSocket connection", { error });
      isConnectingRef.current = false;
      handleConnectionFailure();
    }
  }, [user, handleConnectionSuccess, handleConnectionFailure]);

  // Message sender with queue support
  const sendMessage = useCallback((message: object) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      logger.error("WebSocket not connected, message not sent", { message });
    }
  }, []);

  return {
    sendMessage,
    lastMessage,
    connectionStatus,
    reconnect: connect,
    isConnected: connectionStatus === "Connected",
    reconnectAttempts,
  };
};
```

### 3. **Encryption Utilities**

```typescript
// Generate RSA key pair for user
export const generateKeyPair = async (): Promise<{
  publicKey: CryptoKey;
  privateKey: CryptoKey;
}> => {
  const keyPair = await window.crypto.subtle.generateKey(
    {
      name: "RSA-OAEP",
      modulusLength: 2048,
      publicExponent: new Uint8Array([1, 0, 1]),
      hash: "SHA-256",
    },
    true,
    ["encrypt", "decrypt"]
  );

  return keyPair;
};

// Export public key to PEM format
export const exportPublicKey = async (publicKey: CryptoKey): Promise<string> => {
  const exported = await window.crypto.subtle.exportKey("spki", publicKey);
  const exportedAsString = String.fromCharCode(...new Uint8Array(exported));
  const exportedAsBase64 = window.btoa(exportedAsString);
  
  return `-----BEGIN PUBLIC KEY-----\n${exportedAsBase64}\n-----END PUBLIC KEY-----`;
};

// Hybrid encryption: RSA + AES
export async function encryptMessage(
  message: string,
  publicKeyPem: string
): Promise<string> {
  try {
    // Import recipient's public key
    const publicKey = await importPublicKey(publicKeyPem);

    // Generate a random AES key
    const aesKey = await generateAESKey();

    // Encrypt the AES key with RSA
    const exportedAesKey = await window.crypto.subtle.exportKey("raw", aesKey);
    const encryptedAesKey = await window.crypto.subtle.encrypt(
      { name: "RSA-OAEP" },
      publicKey,
      exportedAesKey
    );

    // Generate a random IV
    const iv = window.crypto.getRandomValues(new Uint8Array(12));

    // Encrypt the message with AES
    const encodedMessage = new TextEncoder().encode(message);
    const encryptedMessage = await window.crypto.subtle.encrypt(
      { name: "AES-GCM", iv: iv },
      aesKey,
      encodedMessage
    );

    // Combine encrypted AES key + IV + encrypted message
    const combined = new Uint8Array(
      encryptedAesKey.byteLength + iv.byteLength + encryptedMessage.byteLength
    );
    combined.set(new Uint8Array(encryptedAesKey), 0);
    combined.set(iv, encryptedAesKey.byteLength);
    combined.set(
      new Uint8Array(encryptedMessage),
      encryptedAesKey.byteLength + iv.byteLength
    );

    // Convert to base64
    return btoa(String.fromCharCode(...combined));
  } catch (error) {
    console.error("Encryption failed:", error);
    throw new Error("Failed to encrypt message");
  }
}

// Decrypt message
export async function decryptMessage(
  encryptedData: string,
  privateKey: CryptoKey
): Promise<string> {
  try {
    // Convert from base64
    const combined = new Uint8Array(
      atob(encryptedData)
        .split("")
        .map((char) => char.charCodeAt(0))
    );

    // Extract components (RSA key size is 256 bytes for 2048-bit key)
    const rsaKeySize = 256;
    const ivSize = 12;
    
    const encryptedAesKey = combined.slice(0, rsaKeySize);
    const iv = combined.slice(rsaKeySize, rsaKeySize + ivSize);
    const encryptedMessage = combined.slice(rsaKeySize + ivSize);

    // Decrypt AES key with RSA private key
    const decryptedAesKey = await window.crypto.subtle.decrypt(
      { name: "RSA-OAEP" },
      privateKey,
      encryptedAesKey
    );

    // Import the AES key
    const aesKey = await window.crypto.subtle.importKey(
      "raw",
      decryptedAesKey,
      { name: "AES-GCM" },
      false,
      ["decrypt"]
    );

    // Decrypt the message
    const decryptedMessage = await window.crypto.subtle.decrypt(
      { name: "AES-GCM", iv: iv },
      aesKey,
      encryptedMessage
    );

    return new TextDecoder().decode(decryptedMessage);
  } catch (error) {
    console.error("Decryption failed:", error);
    throw new Error("Failed to decrypt message");
  }
}
```

### 4. **Chat Components**

#### Main Chat Page
```typescript
const ChatPage: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const {
    sendMessage,
    lastMessage,
    connectionStatus,
    isConnected,
    reconnectAttempts,
  } = useWebSocketImproved();

  const [messages, setMessages] = useState<Message[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [messageText, setMessageText] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  // Handle incoming WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    try {
      const data = JSON.parse(lastMessage.data);
      logger.debug("Received WebSocket message", { type: data.type });

      switch (data.type) {
        case "client_list":
          handleClientList(data);
          break;
        case "chat":
          handleChatMessage(data);
          break;
        case "typing":
          handleTypingIndicator(data);
          break;
        case "delivery_receipt":
          handleDeliveryReceipt(data);
          break;
        default:
          logger.warn("Unknown message type", { type: data.type });
      }
    } catch (error) {
      logger.error("Failed to parse WebSocket message", { error });
    }
  }, [lastMessage]);

  // Handle incoming chat messages
  const handleChatMessage = async (data: any) => {
    try {
      if (!user?.privateKey) {
        logger.error("No private key available for decryption");
        return;
      }

      // Decrypt the message
      const decryptedContent = await decryptMessage(data.content, user.privateKey);

      const newMessage: Message = {
        id: `${Date.now()}-${Math.random()}`,
        content: decryptedContent,
        senderId: data.senderId,
        recipientId: data.recipientId,
        timestamp: new Date(data.timestamp),
        encrypted: true,
        deliveryInfo: {
          delivered: true,
          readAt: new Date(),
        },
      };

      setMessages(prev => [...prev, newMessage]);

      // Send delivery receipt
      sendMessage({
        type: "delivery_receipt",
        messageId: newMessage.id,
        recipientId: data.senderId,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error("Failed to handle chat message", { error });
    }
  };

  // Send a message
  const handleSendMessage = async () => {
    if (!messageText.trim() || !selectedClientId || !isConnected) return;

    const recipient = clients.find(c => c.id === selectedClientId);
    if (!recipient?.publicKey) {
      logger.error("Recipient public key not found");
      return;
    }

    try {
      // Encrypt the message
      const encryptedContent = await encryptMessage(messageText, recipient.publicKey);

      // Create message object
      const message = {
        type: "chat",
        content: encryptedContent,
        recipientId: selectedClientId,
        timestamp: new Date().toISOString(),
      };

      // Send via WebSocket
      sendMessage(message);

      // Add to local messages
      const newMessage: Message = {
        id: `${Date.now()}-${Math.random()}`,
        content: messageText,
        senderId: user!.id,
        recipientId: selectedClientId,
        timestamp: new Date(),
        encrypted: true,
        deliveryInfo: {
          delivered: false,
        },
      };

      setMessages(prev => [...prev, newMessage]);
      setMessageText("");

    } catch (error) {
      logger.error("Failed to send message", { error });
    }
  };

  return (
    <div className="chat-container">
      <ChatHeader 
        user={user} 
        connectionStatus={connectionStatus}
        onLogout={logout}
      />
      
      <div className="chat-body">
        <ChatSidebar
          clients={clients}
          selectedClientId={selectedClientId}
          onSelectClient={setSelectedClientId}
        />
        
        <ChatMainArea
          messages={messages}
          selectedClientId={selectedClientId}
          messageText={messageText}
          onMessageChange={setMessageText}
          onSendMessage={handleSendMessage}
          isConnected={isConnected}
        />
      </div>
    </div>
  );
};
```

## 🎨 UI/UX Features

### 1. **Responsive Design**
- Mobile-first approach
- Flexible grid layouts
- Touch-friendly interfaces
- Adaptive typography

### 2. **Real-time Indicators**
- Connection status display
- Typing indicators
- Message delivery status
- Online/offline user status

### 3. **Accessibility**
- ARIA labels and roles
- Keyboard navigation
- Screen reader support
- High contrast mode

### 4. **Performance Optimizations**
- React.memo for expensive components
- Virtual scrolling for message lists
- Debounced input handling
- Lazy loading of components

## 🔒 Security Implementation

### 1. **Client-side Encryption**
- All encryption happens in browser
- Private keys never leave the client
- Secure key generation and storage

### 2. **Input Sanitization**
```typescript
const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
    .substring(0, MAX_MESSAGE_LENGTH);
};
```

### 3. **Secure Storage**
```typescript
const secureStorage = {
  setItem: (key: string, value: string) => {
    try {
      localStorage.setItem(key, btoa(value)); // Base64 encode
    } catch (error) {
      logger.error("Failed to store item", { key, error });
    }
  },
  
  getItem: (key: string): string | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? atob(item) : null; // Base64 decode
    } catch (error) {
      logger.error("Failed to retrieve item", { key, error });
      return null;
    }
  },
};
```

This frontend architecture provides a secure, performant, and user-friendly interface for encrypted real-time messaging.
