# Cypher Chat - Message Flow Analysis

## 🔄 Complete Message Flow Documentation

This document details the complete flow of messages through the Cypher Chat system, from composition to delivery and acknowledgment.

## 📨 Message Lifecycle Overview

### 1. **Message States**
- **Composing**: User is typing the message
- **Encrypting**: Client-side encryption in progress
- **Sending**: Message being transmitted via WebSocket
- **Delivered**: Message received by server and forwarded
- **Read**: Message decrypted and displayed to recipient
- **Acknowledged**: Delivery receipt sent back to sender

### 2. **Message Types**
```typescript
enum MessageType {
  CHAT = "chat",
  PUBLIC_KEY = "public_key",
  CLIENT_LIST = "client_list",
  TYPING = "typing",
  DELIVERY_RECEIPT = "delivery_receipt",
  PING = "ping",
  PONG = "pong"
}
```

## 🚀 Detailed Message Sending Flow

### Step 1: Message Composition
```typescript
// User types message in the chat interface
const handleMessageInput = (text: string) => {
  setMessageText(text);
  
  // Send typing indicator (debounced)
  if (!isTyping && text.length > 0) {
    setIsTyping(true);
    sendTypingIndicator(true);
    
    // Clear typing indicator after delay
    setTimeout(() => {
      setIsTyping(false);
      sendTypingIndicator(false);
    }, 2000);
  }
};
```

### Step 2: Message Validation
```typescript
const validateMessage = (message: string, recipientId: string): boolean => {
  // Check message length
  if (message.length === 0 || message.length > MAX_MESSAGE_LENGTH) {
    return false;
  }
  
  // Check recipient exists
  if (!recipientId || !clients.find(c => c.id === recipientId)) {
    return false;
  }
  
  // Check connection status
  if (!isConnected) {
    return false;
  }
  
  return true;
};
```

### Step 3: Encryption Process
```typescript
const encryptAndSendMessage = async (
  plaintext: string, 
  recipientId: string
) => {
  try {
    // 1. Get recipient's public key
    const recipient = clients.find(c => c.id === recipientId);
    if (!recipient?.publicKey) {
      throw new Error("Recipient public key not found");
    }

    // 2. Generate random AES key (256-bit)
    const aesKey = await window.crypto.subtle.generateKey(
      { name: "AES-GCM", length: 256 },
      true,
      ["encrypt", "decrypt"]
    );

    // 3. Generate random IV (96-bit for GCM)
    const iv = window.crypto.getRandomValues(new Uint8Array(12));

    // 4. Encrypt message content with AES
    const encodedMessage = new TextEncoder().encode(plaintext);
    const encryptedMessage = await window.crypto.subtle.encrypt(
      { name: "AES-GCM", iv: iv },
      aesKey,
      encodedMessage
    );

    // 5. Export AES key for RSA encryption
    const exportedAesKey = await window.crypto.subtle.exportKey("raw", aesKey);

    // 6. Import recipient's RSA public key
    const publicKey = await importPublicKey(recipient.publicKey);

    // 7. Encrypt AES key with RSA
    const encryptedAesKey = await window.crypto.subtle.encrypt(
      { name: "RSA-OAEP" },
      publicKey,
      exportedAesKey
    );

    // 8. Combine encrypted components
    const combined = new Uint8Array(
      encryptedAesKey.byteLength + iv.byteLength + encryptedMessage.byteLength
    );
    combined.set(new Uint8Array(encryptedAesKey), 0);
    combined.set(iv, encryptedAesKey.byteLength);
    combined.set(
      new Uint8Array(encryptedMessage),
      encryptedAesKey.byteLength + iv.byteLength
    );

    // 9. Convert to base64 for transmission
    const encryptedPayload = btoa(String.fromCharCode(...combined));

    return encryptedPayload;
  } catch (error) {
    logger.error("Encryption failed", { error });
    throw new Error("Failed to encrypt message");
  }
};
```

### Step 4: WebSocket Transmission
```typescript
const sendChatMessage = async (plaintext: string, recipientId: string) => {
  try {
    // Encrypt the message
    const encryptedContent = await encryptAndSendMessage(plaintext, recipientId);

    // Create message object
    const message = {
      type: MessageType.CHAT,
      content: encryptedContent,
      recipientId: recipientId,
      timestamp: new Date().toISOString(),
      messageId: generateMessageId(),
    };

    // Send via WebSocket
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      logger.info("Message sent", { recipientId, messageId: message.messageId });
    } else {
      throw new Error("WebSocket not connected");
    }

    // Add to local message history (optimistic update)
    const localMessage: Message = {
      id: message.messageId,
      content: plaintext, // Store plaintext locally
      senderId: user!.id,
      recipientId: recipientId,
      timestamp: new Date(),
      encrypted: true,
      deliveryInfo: {
        delivered: false,
        sent: true,
      },
    };

    setMessages(prev => [...prev, localMessage]);
    setMessageText("");

  } catch (error) {
    logger.error("Failed to send message", { error });
    // Show error to user
    showErrorNotification("Failed to send message");
  }
};
```

## 📥 Message Receiving Flow

### Step 1: WebSocket Message Reception
```typescript
// WebSocket message handler
socket.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    logger.debug("Received WebSocket message", { type: data.type });

    switch (data.type) {
      case MessageType.CHAT:
        handleIncomingChatMessage(data);
        break;
      case MessageType.CLIENT_LIST:
        handleClientListUpdate(data);
        break;
      case MessageType.TYPING:
        handleTypingIndicator(data);
        break;
      case MessageType.DELIVERY_RECEIPT:
        handleDeliveryReceipt(data);
        break;
      default:
        logger.warn("Unknown message type", { type: data.type });
    }
  } catch (error) {
    logger.error("Failed to parse WebSocket message", { error });
  }
};
```

### Step 2: Message Decryption
```typescript
const handleIncomingChatMessage = async (data: any) => {
  try {
    // Validate message structure
    if (!data.content || !data.senderId || !data.timestamp) {
      logger.error("Invalid message structure", { data });
      return;
    }

    // Check if we have the private key
    if (!user?.privateKey) {
      logger.error("No private key available for decryption");
      return;
    }

    // Decrypt the message
    const decryptedContent = await decryptMessage(data.content, user.privateKey);

    // Create message object
    const newMessage: Message = {
      id: data.messageId || generateMessageId(),
      content: decryptedContent,
      senderId: data.senderId,
      recipientId: user.id,
      timestamp: new Date(data.timestamp),
      encrypted: true,
      deliveryInfo: {
        delivered: true,
        receivedAt: new Date(),
      },
    };

    // Add to message history
    setMessages(prev => {
      // Check for duplicates
      if (prev.some(msg => msg.id === newMessage.id)) {
        return prev;
      }
      return [...prev, newMessage].sort((a, b) => 
        a.timestamp.getTime() - b.timestamp.getTime()
      );
    });

    // Send delivery receipt
    sendDeliveryReceipt(data.senderId, newMessage.id);

    // Show notification if chat is not active
    if (selectedClientId !== data.senderId) {
      showMessageNotification(newMessage);
    }

    // Mark as read if chat is active
    if (selectedClientId === data.senderId && document.hasFocus()) {
      markMessageAsRead(newMessage.id);
    }

  } catch (error) {
    logger.error("Failed to decrypt message", { error });
    // Could show "Failed to decrypt message" placeholder
  }
};
```

### Step 3: Decryption Process
```typescript
const decryptMessage = async (
  encryptedData: string,
  privateKey: CryptoKey
): Promise<string> => {
  try {
    // 1. Convert from base64
    const combined = new Uint8Array(
      atob(encryptedData)
        .split("")
        .map((char) => char.charCodeAt(0))
    );

    // 2. Extract components
    const rsaKeySize = 256; // 2048-bit RSA key = 256 bytes
    const ivSize = 12;      // AES-GCM IV = 12 bytes
    
    const encryptedAesKey = combined.slice(0, rsaKeySize);
    const iv = combined.slice(rsaKeySize, rsaKeySize + ivSize);
    const encryptedMessage = combined.slice(rsaKeySize + ivSize);

    // 3. Decrypt AES key with RSA private key
    const decryptedAesKey = await window.crypto.subtle.decrypt(
      { name: "RSA-OAEP" },
      privateKey,
      encryptedAesKey
    );

    // 4. Import the AES key
    const aesKey = await window.crypto.subtle.importKey(
      "raw",
      decryptedAesKey,
      { name: "AES-GCM" },
      false,
      ["decrypt"]
    );

    // 5. Decrypt the message content
    const decryptedMessage = await window.crypto.subtle.decrypt(
      { name: "AES-GCM", iv: iv },
      aesKey,
      encryptedMessage
    );

    // 6. Convert back to string
    return new TextDecoder().decode(decryptedMessage);

  } catch (error) {
    logger.error("Decryption failed", { error });
    throw new Error("Failed to decrypt message");
  }
};
```

## 🔄 Backend Message Processing

### Step 1: WebSocket Message Reception
```go
func (c *Client) readPump() {
    defer func() {
        c.Server.unregister <- c
        c.Conn.Close()
    }()

    c.Conn.SetReadLimit(maxMessageSize)
    c.Conn.SetReadDeadline(time.Now().Add(pongWait))
    c.Conn.SetPongHandler(func(string) error {
        c.Conn.SetReadDeadline(time.Now().Add(pongWait))
        return nil
    })

    for {
        _, message, err := c.Conn.ReadMessage()
        if err != nil {
            if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                log.Printf("WebSocket error: %v", err)
            }
            break
        }

        // Process message in goroutine to avoid blocking
        go c.handleWebSocketMessage(websocket.TextMessage, message)
    }
}
```

### Step 2: Message Routing
```go
func (c *Client) handleWebSocketMessage(messageType int, payload []byte) {
    var msg Message
    if err := json.Unmarshal(payload, &msg); err != nil {
        log.Printf("Failed to parse message from client %s: %v", c.ID, err)
        return
    }

    // Rate limiting check
    if err := c.Server.rateLimiter.Allow(c.getClientIP(), "message"); err != nil {
        log.Printf("Rate limit exceeded for client %s: %v", c.ID, err)
        return
    }

    switch msg.Type {
    case MessageTypeChat:
        c.handleChatMessage(msg)
    case MessageTypePublicKey:
        c.handlePublicKey(msg)
    case MessageTypeTyping:
        c.handleTypingMessage(msg)
    case MessageTypeDeliveryReceipt:
        c.handleDeliveryReceipt(msg)
    default:
        log.Printf("Unknown message type from client %s: %s", c.ID, msg.Type)
    }
}
```

### Step 3: Chat Message Processing
```go
func (c *Client) handleChatMessage(msg Message) {
    // Validate message
    if err := validateChatMessage(msg); err != nil {
        log.Printf("Invalid chat message from client %s: %v", c.ID, err)
        return
    }

    // Store message in database
    dbMsg := storage.Message{
        ID:          uuid.New().String(),
        SenderId:    c.ID,
        RecipientId: msg.RecipientId,
        Content:     []byte(msg.Content), // Encrypted content
        Type:        msg.Type,
        Timestamp:   time.Now(),
        Delivered:   false,
        Read:        false,
    }

    if err := c.Server.store.StoreMessage(dbMsg); err != nil {
        log.Printf("Failed to store message from client %s: %v", c.ID, err)
        return
    }

    // Forward to recipient if online
    if recipientClient, ok := c.Server.clients.Load(msg.RecipientId); ok {
        recipient := recipientClient.(*Client)
        
        // Prepare message for forwarding
        forwardMsg := Message{
            Type:        MessageTypeChat,
            Content:     msg.Content, // Forward encrypted content as-is
            SenderId:    c.ID,
            RecipientId: msg.RecipientId,
            Timestamp:   time.Now(),
            MessageId:   dbMsg.ID,
        }

        if data, err := json.Marshal(forwardMsg); err == nil {
            select {
            case recipient.send <- data:
                // Update delivery status
                go c.Server.store.UpdateMessageDelivery(dbMsg.ID, true)
                log.Printf("Message forwarded from %s to %s", c.ID, msg.RecipientId)
            default:
                log.Printf("Failed to send message to %s: buffer full", msg.RecipientId)
            }
        }
    } else {
        log.Printf("Recipient %s not online, message stored for later delivery", msg.RecipientId)
    }
}
```

## 📊 Message Delivery Guarantees

### 1. **At-Least-Once Delivery**
- Messages are stored in database before forwarding
- Offline messages are delivered when user comes online
- Delivery receipts confirm successful delivery

### 2. **Message Ordering**
- Timestamps ensure chronological ordering
- Database queries maintain order
- Client-side sorting for display

### 3. **Duplicate Prevention**
```typescript
// Client-side duplicate detection
const addMessage = (newMessage: Message) => {
  setMessages(prev => {
    // Check for duplicates by ID
    if (prev.some(msg => msg.id === newMessage.id)) {
      return prev;
    }
    
    // Add and sort by timestamp
    return [...prev, newMessage].sort((a, b) => 
      a.timestamp.getTime() - b.timestamp.getTime()
    );
  });
};
```

## 🔔 Delivery Receipts & Read Status

### Delivery Receipt Flow
```typescript
// Send delivery receipt
const sendDeliveryReceipt = (senderId: string, messageId: string) => {
  const receipt = {
    type: MessageType.DELIVERY_RECEIPT,
    messageId: messageId,
    recipientId: senderId,
    timestamp: new Date().toISOString(),
    status: "delivered"
  };

  sendMessage(receipt);
};

// Handle delivery receipt
const handleDeliveryReceipt = (data: any) => {
  setMessages(prev => 
    prev.map(msg => 
      msg.id === data.messageId 
        ? { ...msg, deliveryInfo: { ...msg.deliveryInfo, delivered: true } }
        : msg
    )
  );
};
```

## 🚨 Error Handling & Recovery

### 1. **Network Failures**
- Automatic reconnection with exponential backoff
- Message queuing during disconnection
- Retry mechanism for failed sends

### 2. **Encryption Failures**
- Graceful error handling with user notification
- Fallback to error message display
- Key regeneration if needed

### 3. **Server Errors**
- Error response handling
- User notification system
- Automatic retry for transient errors

This comprehensive message flow ensures reliable, secure, and efficient communication in the Cypher Chat application.
