flowchart TD
    A[User types message] --> B[Validate input]
    B --> C{Valid message?}
    C -->|No| D[Show error]
    C -->|Yes| E[Get recipient's public key]
    E --> F[Generate random AES key]
    F --> G[Encrypt message with AES]
    G --> H[Encrypt AES key with RSA]
    H --> I[Combine encrypted data]
    I --> J[Convert to base64]
    J --> K[Create message object]
    K --> L{WebSocket connected?}
    L -->|No| M[Queue message]
    L -->|Yes| N[Send via WebSocket]
    N --> O[Backend receives message]
    O --> P[Validate sender]
    P --> Q[Store in database]
    Q --> R{Recipient online?}
    R -->|No| S[Store for later delivery]
    R -->|Yes| T[Forward to recipient]
    T --> U[Recipient receives]
    U --> V[Decrypt AES key with RSA]
    V --> W[Decrypt message with AES]
    W --> X[Display message]
    X --> Y[Send delivery receipt]
    Y --> Z[Update sender UI]
    
    style A fill:#e3f2fd
    style G fill:#fff3e0
    style H fill:#fff3e0
    style Q fill:#e8f5e8
    style X fill:#f3e5f5
