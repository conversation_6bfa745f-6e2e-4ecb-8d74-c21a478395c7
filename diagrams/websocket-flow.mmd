sequenceDiagram
    participant C1 as Client 1
    participant WS as WebSocket Server
    participant DB as Database
    participant C2 as Client 2
    
    Note over C1,C2: Connection Establishment
    C1->>WS: Connect with userId
    WS->>WS: Generate clientId
    WS->>C1: Connection established
    
    Note over C1,C2: Public Key Exchange
    C1->>WS: Send public_key message
    WS->>WS: Store public key
    WS->>C1: Broadcast client_list
    WS->>C2: Broadcast client_list
    
    Note over C1,C2: Message Flow
    C1->>C1: Encrypt message with C2's public key
    C1->>WS: Send encrypted chat message
    WS->>DB: Store encrypted message
    WS->>C2: Forward encrypted message
    C2->>C2: Decrypt with private key
    C2->>C2: Display message
    
    Note over C1,C2: Delivery Confirmation
    C2->>WS: Send delivery_receipt
    WS->>C1: Forward delivery receipt
    C1->>C1: Update message status
    
    Note over C1,C2: Typing Indicators
    C1->>WS: Send typing indicator
    WS->>C2: Forward typing indicator
    C2->>C2: Show "User is typing..."
    
    Note over C1,C2: Heartbeat
    loop Every 30 seconds
        C1->>WS: ping
        WS->>C1: pong
    end
    
    Note over C1,C2: Disconnection
    C1->>WS: Close connection
    WS->>WS: Remove from client list
    WS->>C2: Broadcast updated client_list
