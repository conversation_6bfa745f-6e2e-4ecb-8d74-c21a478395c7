# Docker Compose Guide for Cypher Chat

This guide explains how to use Docker Compose to run the Cypher Chat application.

## Overview

The Cypher Chat application consists of three main components:

1. **PostgreSQL Database**: Stores user accounts, messages, and other data
2. **Backend API**: Go server that handles authentication, WebSocket connections, and message routing
3. **Frontend**: React application that provides the user interface

## Prerequisites

- Docker and Docker Compose installed on your system
- Basic understanding of Docker concepts

## Docker Compose Configuration

The `docker-compose.yml` file defines the services, networks, and volumes for the application.

### Services

#### PostgreSQL

```yaml
postgres:
  image: postgres:15-alpine
  container_name: cypher-chat-postgres
  environment:
    POSTGRES_USER: cypherchat
    POSTGRES_PASSWORD: cypherchat_password
    POSTGRES_DB: cypherchat
  ports:
    - "5432:5432"
  volumes:
    - ./postgres_data:/var/lib/postgresql/data
    - ./backend/db/init:/docker-entrypoint-initdb.d
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U cypherchat"]
    interval: 5s
    timeout: 5s
    retries: 5
  restart: unless-stopped
```

- Uses the official PostgreSQL 15 Alpine image
- Sets up database credentials
- Maps port 5432 for database access
- Persists data in the `postgres_data` directory
- Runs initialization scripts from `backend/db/init`
- Includes a health check to ensure the database is ready
- Automatically restarts unless explicitly stopped

#### Backend

```yaml
backend:
  build:
    context: ./backend
    dockerfile: Dockerfile
  container_name: cypher-chat-backend
  depends_on:
    postgres:
      condition: service_healthy
  environment:
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USER: cypherchat
    DB_PASSWORD: cypherchat_password
    DB_NAME: cypherchat
    PORT: 8080
  ports:
    - "8080:8080"
  restart: unless-stopped
```

- Built from the Dockerfile in the `backend` directory
- Depends on the PostgreSQL service being healthy
- Configures database connection via environment variables
- Maps port 8080 for API access
- Automatically restarts unless explicitly stopped

#### Frontend

```yaml
frontend:
  build:
    context: ./frontend
    dockerfile: Dockerfile
  container_name: cypher-chat-frontend
  depends_on:
    - backend
  ports:
    - "3000:3000"
  restart: unless-stopped
```

- Built from the Dockerfile in the `frontend` directory
- Depends on the backend service
- Maps port 3000 for web access
- Automatically restarts unless explicitly stopped

## Common Commands

### Starting the Application

```bash
docker-compose up -d
```

This command starts all services in detached mode (background).

### Stopping the Application

```bash
docker-compose down
```

This command stops and removes all containers.

### Viewing Logs

```bash
docker-compose logs
```

To view logs for a specific service:

```bash
docker-compose logs backend
```

To follow logs in real-time:

```bash
docker-compose logs -f
```

### Rebuilding Services

If you make changes to the code, you need to rebuild the services:

```bash
docker-compose build
```

To rebuild a specific service:

```bash
docker-compose build frontend
```

### Restarting Services

To restart all services:

```bash
docker-compose restart
```

To restart a specific service:

```bash
docker-compose restart backend
```

## Accessing the Application

- Frontend: http://localhost:3000
- Backend API: http://localhost:8080
- PostgreSQL: localhost:5432 (username: cypherchat, password: cypherchat_password)

## Troubleshooting

### Container Won't Start

Check the logs:

```bash
docker-compose logs [service_name]
```

### Database Connection Issues

Ensure the PostgreSQL container is running:

```bash
docker-compose ps postgres
```

Check if the database is initialized:

```bash
docker exec -it cypher-chat-postgres psql -U cypherchat -d cypherchat -c "\dt"
```

### Frontend Can't Connect to Backend

Check if the backend container is running:

```bash
docker-compose ps backend
```

Verify the backend is accessible:

```bash
curl http://localhost:8080/health
```

## Data Persistence

The PostgreSQL data is stored in the `postgres_data` directory. This ensures that your data persists even if the containers are removed.

To completely reset the database, you can remove this directory:

```bash
docker-compose down
rm -rf postgres_data
docker-compose up -d
```

## Custom Scripts

The repository includes several helper scripts:

- `rebuild-frontend.sh`: Rebuilds and restarts only the frontend container
- `apply-changes.sh`: Applies configuration changes to the application

## Security Considerations

- The default database credentials should be changed in production
- HTTPS should be enabled for production deployments
- The JWT secret should be securely generated and stored
