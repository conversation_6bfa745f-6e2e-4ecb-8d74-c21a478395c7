# Docker Compose configuration for Cypher Chat

services:
  postgres:
    image: postgres:15-alpine
    container_name: cypher-chat-postgres
    environment:
      POSTGRES_USER: cypherchat
      POSTGRES_PASSWORD: cypherchat_password
      POSTGRES_DB: cypherchat
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/db/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cypherchat"]
      interval: 5s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cypher-chat-backend
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: cypherchat
      DB_PASSWORD: cypherchat_password
      DB_NAME: cypherchat
      PORT: 8080
    ports:
      - "8080:8080"
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: cypher-chat-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
