{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/node-forge": "^1.3.11", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.20", "buffer": "^6.0.3", "framer-motion": "^11.13.1", "node-forge": "^1.3.1", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.0.2", "tailwindcss": "^3.4.16"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}