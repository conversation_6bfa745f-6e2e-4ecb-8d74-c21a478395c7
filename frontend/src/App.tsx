import React from "react";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import ErrorBoundary from "./components/ErrorBoundary";
import ProtectedRoute from "./components/ProtectedRoute";
import { AuthProvider } from "./contexts/AuthContextImproved";
import ChatPage from "./pages/ChatPage";
import LoginPage from "./pages/LoginPage";

const App: React.FC = () => {
	return (
		<ErrorBoundary>
			<BrowserRouter>
				<AuthProvider>
					<Routes>
						<Route path='/' element={<LoginPage />} />
						<Route
							path='/chat'
							element={
								<ProtectedRoute>
									<ChatPage />
								</ProtectedRoute>
							}
						/>
						{/* Redirect any unknown routes to home */}
						<Route path='*' element={<Navigate to='/' replace />} />
					</Routes>
				</AuthProvider>
			</BrowserRouter>
		</ErrorBoundary>
	);
};

export default App;
