import React from "react";
import SoundToggle from "./SoundToggle";
import UserAvatar from "./UserAvatar";

interface Client {
	id: string;
	publicKey: string;
	isOnline: boolean;
	status: string;
}

interface ChatHeaderProps {
	selectedClient: Client;
	remoteTyping: { [key: string]: boolean };
	soundsEnabled: boolean;
	onSoundToggle: (enabled: boolean) => void;
	onSettingsOpen: () => void;
	onMobileMenuToggle: () => void;
	isMobileMenuOpen: boolean;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
	selectedClient,
	remoteTyping,
	soundsEnabled,
	onSoundToggle,
	onSettingsOpen,
	onMobileMenuToggle,
	isMobileMenuOpen,
}) => {
	return (
		<div className='p-6 border-b border-zinc-800/60 bg-gradient-to-r from-zinc-900/80 to-zinc-800/80 backdrop-blur-md shadow-lg'>
			<div className='flex items-center justify-between'>
				<div className='flex items-center gap-4'>
					<UserAvatar
						userId={selectedClient.id}
						size='lg'
						showStatus={true}
						isOnline={selectedClient.isOnline}
					/>
					<div>
						<h2 className='font-semibold text-lg text-white'>
							User {selectedClient.id.slice(0, 8)}
						</h2>
						<div className='text-sm text-zinc-400 flex items-center gap-2'>
							{remoteTyping[selectedClient.id] ? (
								<span className='text-emerald-400 flex items-center gap-2'>
									<div className='flex space-x-1'>
										<span
											className='w-1.5 h-1.5 bg-emerald-400 rounded-full animate-bounce'
											style={{ animationDelay: "0ms" }}
										></span>
										<span
											className='w-1.5 h-1.5 bg-emerald-400 rounded-full animate-bounce'
											style={{ animationDelay: "150ms" }}
										></span>
										<span
											className='w-1.5 h-1.5 bg-emerald-400 rounded-full animate-bounce'
											style={{ animationDelay: "300ms" }}
										></span>
									</div>
									<span className='font-medium'>typing...</span>
								</span>
							) : selectedClient.isOnline ? (
								<>
									<div className='w-2 h-2 bg-emerald-500 rounded-full animate-pulse'></div>
									<span className='font-medium text-emerald-400'>Online</span>
								</>
							) : (
								<>
									<div className='w-2 h-2 bg-zinc-500 rounded-full'></div>
									<span>Offline</span>
								</>
							)}
						</div>
					</div>
				</div>
				<div className='flex items-center gap-2'>
					{/* Mobile menu button */}
					<button
						onClick={onMobileMenuToggle}
						className='md:hidden p-2 rounded-lg hover:bg-zinc-800/60 text-zinc-400 hover:text-white transition-colors'
						title='Menu'
					>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M4 6h16M4 12h16M4 18h16'
							/>
						</svg>
					</button>

					<SoundToggle initialState={soundsEnabled} onChange={onSoundToggle} />
					<button
						onClick={onSettingsOpen}
						className='p-2 rounded-full hover:bg-zinc-800 text-zinc-400 hover:text-white transition-colors'
						title='Settings'
					>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={1.5}
								d='M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
							/>
						</svg>
					</button>
					<button className='p-2 rounded-full hover:bg-zinc-800 text-zinc-400 hover:text-white transition-colors'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={1.5}
								d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'
							/>
						</svg>
					</button>
				</div>
			</div>
		</div>
	);
};

export default ChatHeader;
