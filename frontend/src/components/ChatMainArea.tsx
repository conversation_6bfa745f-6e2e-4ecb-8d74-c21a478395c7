import React from 'react';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

interface Message {
	id: string;
	content: string;
	senderId: string;
	timestamp: string;
	encrypted: boolean;
	delivered: boolean;
	read: boolean;
}

interface ChatMainAreaProps {
	messages: Message[];
	isLoading: boolean;
	isSending: boolean;
	onSendMessage: (content: string) => void;
	onTyping: (isTyping: boolean) => void;
}

const ChatMainArea: React.FC<ChatMainAreaProps> = ({
	messages,
	isLoading,
	isSending,
	onSendMessage,
	onTyping,
}) => {
	return (
		<>
			<MessageList messages={messages} isLoading={isLoading} />

			<div className='p-4 border-t border-zinc-800/80 bg-zinc-900/50 backdrop-blur-sm'>
				<MessageInput
					onSendMessage={onSendMessage}
					onTyping={onTyping}
					disabled={isSending}
				/>
				<div className='text-xs text-zinc-500 mt-2 flex items-center gap-1'>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-3 w-3'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
						/>
					</svg>
					Messages are end-to-end encrypted
				</div>
			</div>
		</>
	);
};

export default ChatMainArea;
