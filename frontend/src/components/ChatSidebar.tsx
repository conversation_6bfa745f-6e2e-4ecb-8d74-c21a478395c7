import React from 'react';
import ClientList from './ClientList';
import ConnectionStatus from './ConnectionStatus';

interface Client {
	id: string;
	publicKey: string;
	isOnline: boolean;
	status: string;
}

interface ChatSidebarProps {
	clients: Client[];
	selectedClient: Client | null;
	onClientSelect: (client: Client) => void;
	onLogout: () => void;
	connectionStatus: string;
	onReconnect: () => void;
	isMobileMenuOpen: boolean;
	onMobileMenuClose: () => void;
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
	clients,
	selectedClient,
	onClientSelect,
	onLogout,
	connectionStatus,
	onReconnect,
	isMobileMenuOpen,
	onMobileMenuClose,
}) => {
	return (
		<div className={`w-72 md:w-80 lg:w-72 border-r border-zinc-800/60 flex flex-col bg-zinc-900/80 backdrop-blur-sm transition-transform duration-300 z-50 ${
			isMobileMenuOpen ? 'fixed inset-y-0 left-0 translate-x-0' : 'hidden md:flex'
		}`}>
			<div className='p-5 border-b border-zinc-800/60 flex justify-between items-center bg-gradient-to-r from-zinc-900/90 to-zinc-800/90'>
				<div className='flex items-center gap-3'>
					<div className='w-9 h-9 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-900/30'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5 text-white'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
							/>
						</svg>
					</div>
					<h1 className='font-bold text-xl bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent'>
						Cypher Chat
					</h1>
				</div>
				<div className='flex items-center gap-2'>
					{/* Mobile close button */}
					<button
						onClick={onMobileMenuClose}
						className='md:hidden p-2 rounded-lg hover:bg-zinc-800/60 text-zinc-400 hover:text-white transition-colors'
						title='Close menu'
					>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>

					<button
						onClick={onLogout}
						className='text-sm text-zinc-400 hover:text-white transition-all duration-200 px-3 py-2 rounded-lg hover:bg-zinc-800/60 border border-transparent hover:border-zinc-700/50 group'
					>
						<span className='flex items-center gap-2'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-4 w-4 transition-transform group-hover:translate-x-0.5'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1'
								/>
							</svg>
							Logout
						</span>
					</button>
				</div>
			</div>
			<div className='p-5 border-b border-zinc-800/60 flex-1 overflow-hidden'>
				<div className='flex items-center justify-between mb-4'>
					<h2 className='font-semibold text-zinc-300 text-sm uppercase tracking-wider'>
						Contacts
					</h2>
					<div className='flex items-center gap-2'>
						<div className='w-2 h-2 bg-emerald-500 rounded-full animate-pulse'></div>
						<span className='text-xs text-zinc-500'>
							{clients.length} online
						</span>
					</div>
				</div>
				<div className='overflow-y-auto h-full'>
					<ClientList
						clients={clients}
						selectedClient={selectedClient}
						onClientSelect={(client) => {
							onClientSelect(client);
						}}
					/>
				</div>
			</div>
			<div className='mt-auto p-4 border-t border-zinc-800/60 bg-zinc-900/60'>
				<ConnectionStatus status={connectionStatus} onReconnect={onReconnect} />
			</div>
		</div>
	);
};

export default ChatSidebar;
