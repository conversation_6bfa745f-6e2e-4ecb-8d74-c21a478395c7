import React, { useState } from "react";
import UserAvatar from "./UserAvatar";

interface Client {
	id: string;
	publicKey: string;
	isOnline: boolean;
	status: string;
	username?: string;
	lastSeen?: string;
}

interface ClientListProps {
	clients: Client[];
	selectedClient: Client | null;
	onClientSelect: (client: Client) => void;
	currentUserId?: string;
}

const ClientList: React.FC<ClientListProps> = ({
	clients,
	selectedClient,
	onClientSelect,
	currentUserId,
}) => {
	const [searchTerm, setSearchTerm] = useState("");

	// Filter out current user and apply search filter
	const filteredClients = clients.filter((client) => {
		// Don't show current user in the list
		if (client.id === currentUserId) return false;

		// Apply search filter if there is a search term
		if (searchTerm) {
			const displayName = client.username || `User ${client.id.slice(0, 8)}`;
			return displayName.toLowerCase().includes(searchTerm.toLowerCase());
		}

		return true;
	});

	// Sort clients: online first, then by status/name
	const sortedClients = [...filteredClients].sort((a, b) => {
		// Online users first
		if (a.isOnline !== b.isOnline) {
			return a.isOnline ? -1 : 1;
		}

		// Then by name
		const nameA = a.username || `User ${a.id.slice(0, 8)}`;
		const nameB = b.username || `User ${b.id.slice(0, 8)}`;
		return nameA.localeCompare(nameB);
	});

	return (
		<div className='flex flex-col h-full'>
			{/* Search bar */}
			<div className='p-3 border-b border-zinc-800'>
				<div className='relative'>
					<input
						type='text'
						placeholder='Search contacts...'
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className='w-full bg-zinc-800/50 text-zinc-300 rounded-lg pl-9 pr-4 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-emerald-500 placeholder-zinc-500'
					/>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='absolute left-3 top-2.5 h-4 w-4 text-zinc-500'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
						/>
					</svg>
					{searchTerm && (
						<button
							onClick={() => setSearchTerm("")}
							className='absolute right-3 top-2.5 text-zinc-500 hover:text-zinc-300'
						>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-4 w-4'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={2}
									d='M6 18L18 6M6 6l12 12'
								/>
							</svg>
						</button>
					)}
				</div>
			</div>

			{/* Client list */}
			<div className='flex-1 overflow-y-auto space-y-2 p-3 custom-scrollbar'>
				{sortedClients.length > 0 && (
					<div className='text-xs font-medium text-zinc-500 uppercase tracking-wider px-2 mb-2'>
						{searchTerm ? "Search Results" : "Contacts"}
					</div>
				)}

				{sortedClients.map((client) => (
					<button
						key={client.id}
						onClick={() => onClientSelect(client)}
						className={`w-full p-3 rounded-xl flex items-center gap-3 transition-all duration-200 interactive-hover glass-effect
                        ${
													selectedClient?.id === client.id
														? "bg-gradient-to-r from-emerald-900/50 to-emerald-800/40 text-emerald-300 border-glow shadow-enhanced"
														: "hover:bg-gradient-to-r hover:from-zinc-800/70 hover:to-zinc-700/50 text-zinc-300 border border-transparent hover:border-zinc-700/50"
												}`}
					>
						<UserAvatar
							userId={client.id}
							username={client.username}
							size='md'
							showStatus={true}
							isOnline={client.isOnline}
							showTooltip={true}
						/>
						<div className='flex-1 text-left'>
							<div className='font-medium'>
								{client.username || `User ${client.id.slice(0, 8)}`}
							</div>
							<div className='text-xs text-zinc-500 flex items-center'>
								{client.isOnline ? (
									<>
										<span className='inline-block w-1.5 h-1.5 rounded-full bg-emerald-500 mr-1.5 animate-pulse'></span>
										{client.status || "Online"}
									</>
								) : (
									<>
										<span className='inline-block w-1.5 h-1.5 rounded-full bg-zinc-600 mr-1.5'></span>
										{client.lastSeen
											? `Last seen ${formatLastSeen(client.lastSeen)}`
											: "Offline"}
									</>
								)}
							</div>
						</div>
					</button>
				))}

				{sortedClients.length === 0 && (
					<div className='text-center py-12 px-4'>
						<div className='w-16 h-16 mx-auto mb-4 rounded-full bg-zinc-800 flex items-center justify-center'>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-8 w-8 text-zinc-500'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={1.5}
									d='M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z'
								/>
							</svg>
						</div>
						<h3 className='text-zinc-300 font-medium text-lg'>
							{searchTerm ? "No results found" : "No contacts yet"}
						</h3>
						<p className='text-zinc-500 mt-1'>
							{searchTerm
								? `Try a different search term`
								: `Wait for other users to connect or share your invite link`}
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

// Helper function to format last seen time
const formatLastSeen = (timestamp: string): string => {
	const date = new Date(timestamp);
	const now = new Date();
	const diffMs = now.getTime() - date.getTime();
	const diffMins = Math.floor(diffMs / 60000);
	const diffHours = Math.floor(diffMins / 60);
	const diffDays = Math.floor(diffHours / 24);

	if (diffMins < 1) return "just now";
	if (diffMins < 60) return `${diffMins}m ago`;
	if (diffHours < 24) return `${diffHours}h ago`;
	if (diffDays === 1) return "yesterday";
	if (diffDays < 7) return `${diffDays}d ago`;

	return date.toLocaleDateString();
};

export default ClientList;
