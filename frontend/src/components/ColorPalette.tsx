import React from 'react';

interface ColorPaletteProps {
	className?: string;
}

const ColorPalette: React.FC<ColorPaletteProps> = ({ className = '' }) => {
	const colors = [
		{
			name: 'Light Sage Green',
			value: '#96ceb4',
			className: 'bg-light-sage-green',
			description: 'Calming secondary color'
		},
		{
			name: 'Cerulean Blue',
			value: '#1398b9',
			className: 'bg-cerulean-blue',
			description: 'Primary brand color'
		},
		{
			name: 'Ivory Cream',
			value: '#fcfaee',
			className: 'bg-ivory-cream',
			description: 'Background & surfaces'
		},
		{
			name: 'Chartreuse Neon',
			value: '#dfff00',
			className: 'bg-chartreuse-neon',
			description: 'Warning & highlights'
		},
		{
			name: 'Minty Aqua',
			value: '#31ffb6',
			className: 'bg-minty-aqua',
			description: 'Accent & interactions'
		},
		{
			name: 'Baby Blue',
			value: '#98e4ff',
			className: 'bg-baby-blue',
			description: 'Soft surfaces'
		},
		{
			name: 'Crimson Red',
			value: '#ff204e',
			className: 'bg-crimson-red',
			description: 'Errors & alerts'
		}
	];

	return (
		<div className={`p-6 bg-ivory-cream rounded-xl shadow-lg ${className}`}>
			<h3 className="text-xl font-bold text-cerulean-blue mb-4">
				Cypher Chat Color Palette
			</h3>
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{colors.map((color) => (
					<div
						key={color.name}
						className="bg-white rounded-lg p-4 shadow-md border border-cerulean-blue/10"
					>
						<div
							className={`w-full h-16 rounded-lg mb-3 ${color.className} shadow-inner`}
						/>
						<h4 className="font-semibold text-gray-800 text-sm">
							{color.name}
						</h4>
						<p className="text-xs text-gray-600 font-mono">
							{color.value}
						</p>
						<p className="text-xs text-gray-500 mt-1">
							{color.description}
						</p>
					</div>
				))}
			</div>
			
			{/* Color combinations preview */}
			<div className="mt-6">
				<h4 className="text-lg font-semibold text-cerulean-blue mb-3">
					Color Combinations
				</h4>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					{/* Primary combination */}
					<div className="bg-gradient-to-r from-cerulean-blue to-minty-aqua p-4 rounded-lg text-white">
						<h5 className="font-semibold">Primary Gradient</h5>
						<p className="text-sm opacity-90">Buttons & CTAs</p>
					</div>
					
					{/* Background combination */}
					<div className="bg-gradient-to-r from-ivory-cream to-baby-blue p-4 rounded-lg text-gray-800 border border-cerulean-blue/20">
						<h5 className="font-semibold">Background Gradient</h5>
						<p className="text-sm opacity-80">Main surfaces</p>
					</div>
					
					{/* Secondary combination */}
					<div className="bg-gradient-to-r from-light-sage-green to-baby-blue p-4 rounded-lg text-gray-800">
						<h5 className="font-semibold">Secondary Gradient</h5>
						<p className="text-sm opacity-80">Sidebars & panels</p>
					</div>
					
					{/* Accent combination */}
					<div className="bg-gradient-to-r from-minty-aqua to-chartreuse-neon p-4 rounded-lg text-gray-800">
						<h5 className="font-semibold">Accent Gradient</h5>
						<p className="text-sm opacity-80">Highlights & focus</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ColorPalette;
