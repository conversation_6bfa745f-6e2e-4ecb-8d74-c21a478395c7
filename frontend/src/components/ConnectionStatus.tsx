import React from "react";

interface ConnectionStatusProps {
	status: "Connecting" | "Connected" | "Disconnected" | "Error";
	onReconnect: () => void;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
	status,
	onReconnect,
}) => {
	const getStatusColor = () => {
		switch (status) {
			case "Connected":
				return "bg-emerald-500";
			case "Connecting":
				return "bg-yellow-400 animate-pulse";
			case "Disconnected":
				return "bg-zinc-400";
			case "Error":
				return "bg-red-500";
			default:
				return "bg-zinc-400";
		}
	};

	const getStatusIcon = () => {
		switch (status) {
			case "Connected":
				return (
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-4 w-4 text-emerald-500'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M5 13l4 4L19 7'
						/>
					</svg>
				);
			case "Connecting":
				return (
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-4 w-4 text-yellow-400 animate-spin'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
						/>
					</svg>
				);
			case "Disconnected":
				return (
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-4 w-4 text-zinc-400'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414'
						/>
					</svg>
				);
			case "Error":
				return (
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-4 w-4 text-red-500'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
						/>
					</svg>
				);
			default:
				return null;
		}
	};

	// For sidebar display
	if (status === "Connected") {
		return (
			<div className='flex items-center gap-2 text-sm text-zinc-400'>
				<div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
				<span>Connected</span>
			</div>
		);
	}

	// For notification display
	return (
		<>
			{/* Sidebar display */}
			<div className='flex items-center gap-2 text-sm text-zinc-400'>
				<div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
				<span>{status}</span>
				{(status === "Disconnected" || status === "Error") && (
					<button
						onClick={onReconnect}
						className='ml-1 text-emerald-500 hover:text-emerald-400 text-xs transition-colors'
					>
						Reconnect
					</button>
				)}
			</div>

			{/* Notification popup */}
			<div
				className='fixed bottom-4 right-4 flex items-center gap-3 p-3 rounded-lg text-sm
                        bg-zinc-800/90 backdrop-blur-sm text-zinc-200 shadow-lg border border-zinc-700/50 z-50'
			>
				{getStatusIcon()}
				<span>{status}</span>
				{(status === "Disconnected" || status === "Error") && (
					<button
						onClick={onReconnect}
						className='ml-2 px-2 py-1 bg-zinc-700 hover:bg-zinc-600
                                rounded text-xs transition-colors'
					>
						Reconnect
					</button>
				)}
			</div>
		</>
	);
};

export default ConnectionStatus;
