import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import logger from '../utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false 
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return { 
      hasError: true,
      error 
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to our custom logger
    logger.error('Unhandled React Error', {
      error: error.toString(),
      componentStack: errorInfo.componentStack
    });
  }

  handleRecover = () => {
    this.setState({ 
      hasError: false,
      error: undefined,
      errorInfo: undefined 
    });
  }

  render() {
    if (this.state.hasError) {
      // Custom error UI
      return (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-terminal-bg text-terminal-text flex flex-col items-center justify-center p-4 z-50"
        >
          <div className="text-center max-w-md">
            <h1 className="text-3xl font-bold text-red-500 mb-4">
              🚨 Application Error
            </h1>
            
            <div className="bg-gray-900 p-4 rounded-lg mb-4 overflow-auto max-h-64">
              <pre className="text-xs text-red-300">
                {this.state.error?.toString()}
              </pre>
            </div>

            <div className="space-y-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={this.handleRecover}
                className="bg-terminal-accent text-terminal-bg px-4 py-2 rounded hover:bg-terminal-text transition-colors"
              >
                Recover Application
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => window.location.reload()}
                className="bg-red-700 text-white px-4 py-2 rounded ml-2 hover:bg-red-600 transition-colors"
              >
                Reload Page
              </motion.button>
            </div>

            <p className="text-xs text-gray-500 mt-4">
              An unexpected error occurred. Please report this to support.
            </p>
          </div>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
