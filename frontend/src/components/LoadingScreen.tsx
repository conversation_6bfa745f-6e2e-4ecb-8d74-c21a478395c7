import React from 'react';
import { motion } from 'framer-motion';

interface LoadingScreenProps {
    message: string;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ message }) => {
    return (
        <div className="flex h-screen items-center justify-center bg-zinc-950">
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center"
            >
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-emerald-500 mx-auto" />
                <p className="mt-4 text-emerald-400">{message}</p>
            </motion.div>
        </div>
    );
};

export default LoadingScreen; 