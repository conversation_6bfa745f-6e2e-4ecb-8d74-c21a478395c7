import React, { KeyboardEvent, useEffect, useRef, useState } from "react";

interface MessageInputProps {
	onSendMessage: (message: string) => void;
	onTyping?: (isTyping: boolean) => void;
	disabled?: boolean;
	recipientName?: string;
	isRecipientOnline?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
	onSendMessage,
	onTyping,
	disabled,
	recipientName,
	isRecipientOnline,
}) => {
	const [message, setMessage] = useState("");
	const [rows, setRows] = useState(1);
	const [isTyping, setIsTyping] = useState(false);
	const [showEmojiPicker, setShowEmojiPicker] = useState(false);
	const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const emojiPickerRef = useRef<HTMLDivElement>(null);

	// Close emoji picker when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				emojiPickerRef.current &&
				!emojiPickerRef.current.contains(event.target as Node)
			) {
				setShowEmojiPicker(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleSubmit = () => {
		if (message.trim() && !disabled) {
			onSendMessage(message.trim());
			setMessage("");
			setRows(1);

			// Clear typing indicator when sending a message
			if (onTyping && isTyping) {
				setIsTyping(false);
				onTyping(false);
			}
		}
	};

	const handleKeyPress = (e: KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSubmit();
		}
	};

	const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		const newMessage = e.target.value;
		setMessage(newMessage);

		// Auto-resize textarea based on content
		const lineCount = newMessage.split("\n").length;
		setRows(Math.min(Math.max(lineCount, 1), 5)); // Limit to 5 rows max

		// Handle typing indicator
		if (onTyping) {
			// If user wasn't typing before, set typing to true
			if (!isTyping && newMessage.trim().length > 0) {
				setIsTyping(true);
				onTyping(true);
			}

			// If user was typing and now message is empty, set typing to false
			if (isTyping && newMessage.trim().length === 0) {
				setIsTyping(false);
				onTyping(false);
			}

			// Clear previous timeout
			if (typingTimeoutRef.current) {
				clearTimeout(typingTimeoutRef.current);
			}

			// Set timeout to clear typing indicator after 3 seconds of inactivity
			if (newMessage.trim().length > 0) {
				typingTimeoutRef.current = setTimeout(() => {
					if (isTyping) {
						setIsTyping(false);
						onTyping(false);
					}
				}, 3000);
			}
		}
	};

	// Clean up timeout on unmount
	useEffect(() => {
		return () => {
			if (typingTimeoutRef.current) {
				clearTimeout(typingTimeoutRef.current);
			}
		};
	}, []);

	// Add emoji to message
	const addEmoji = (emoji: string) => {
		setMessage((prev) => prev + emoji);
		setShowEmojiPicker(false);

		// Focus the textarea after adding emoji
		if (textareaRef.current) {
			textareaRef.current.focus();
		}
	};

	return (
		<div className='flex flex-col'>
			{/* Recipient status */}
			{recipientName && (
				<div className='px-4 py-2 text-xs text-zinc-400'>
					{isRecipientOnline ? (
						<span className='flex items-center'>
							<span className='inline-block w-1.5 h-1.5 rounded-full bg-emerald-500 mr-1.5 animate-pulse'></span>
							{recipientName} is online
						</span>
					) : (
						<span className='flex items-center'>
							<span className='inline-block w-1.5 h-1.5 rounded-full bg-zinc-600 mr-1.5'></span>
							{recipientName} is offline
						</span>
					)}
				</div>
			)}

			{/* Message input area */}
			<div className='flex gap-3 items-end p-4 bg-gradient-to-r from-baby-blue/40 to-light-sage-green/40 backdrop-blur-sm border-t border-cerulean-blue/30'>
				<div className='flex-1 relative'>
					<textarea
						ref={textareaRef}
						value={message}
						onChange={handleChange}
						onKeyDown={handleKeyPress}
						disabled={disabled}
						placeholder={
							disabled
								? "Select a contact to start chatting"
								: "Type your message..."
						}
						className='w-full bg-gradient-to-br from-ivory-cream/90 to-baby-blue/30 text-gray-800 rounded-xl p-4 pr-12
                              resize-none min-h-[48px] max-h-[120px] border border-cerulean-blue/30
                              disabled:opacity-50 disabled:cursor-not-allowed
                              focus:outline-none focus:ring-2 focus:ring-minty-aqua/50 focus:border-minty-aqua/50
                              transition-all duration-200 shadow-lg placeholder-gray-500'
						rows={rows}
					/>
					<div className='absolute right-3 bottom-3 flex space-x-2'>
						<button
							onClick={() => setShowEmojiPicker(!showEmojiPicker)}
							className='text-gray-500 hover:text-minty-aqua p-1.5 rounded-lg hover:bg-light-sage-green/40 transition-all duration-200 group'
							title='Add emoji'
							disabled={disabled}
						>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-5 w-5'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={1.5}
									d='M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
								/>
							</svg>
						</button>

						{/* Attachment button */}
						<button
							className='text-zinc-400 hover:text-zinc-200 p-1 rounded-full hover:bg-zinc-700/50 transition-colors'
							title='Attach file'
							disabled={disabled}
						>
							<svg
								xmlns='http://www.w3.org/2000/svg'
								className='h-5 w-5'
								fill='none'
								viewBox='0 0 24 24'
								stroke='currentColor'
							>
								<path
									strokeLinecap='round'
									strokeLinejoin='round'
									strokeWidth={1.5}
									d='M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13'
								/>
							</svg>
						</button>
					</div>

					{/* Emoji picker */}
					{showEmojiPicker && (
						<div
							ref={emojiPickerRef}
							className='absolute bottom-full mb-2 right-0 emoji-picker rounded-xl shadow-enhanced-lg p-3 border border-zinc-700/50 z-50'
						>
							<div className='grid grid-cols-8 gap-1'>
								{[
									"\ud83d\ude00",
									"\ud83d\ude02",
									"\ud83d\ude0a",
									"\ud83d\ude0d",
									"\ud83d\ude0e",
									"\ud83d\ude44",
									"\ud83d\ude34",
									"\ud83e\udd14",
									"\ud83d\udc4d",
									"\ud83d\udc4e",
									"\ud83d\udc4f",
									"\ud83d\ude4f",
									"\ud83d\udd25",
									"\u2764\ufe0f",
									"\u2b50",
									"\ud83c\udf89",
								].map((emoji) => (
									<button
										key={emoji}
										onClick={() => addEmoji(emoji)}
										className='text-xl hover:bg-zinc-700 p-1 rounded transition-colors'
									>
										{emoji}
									</button>
								))}
							</div>
						</div>
					)}
				</div>

				{/* Send button */}
				<button
					onClick={handleSubmit}
					disabled={!message.trim() || disabled}
					className='px-5 py-3 bg-gradient-to-r from-cerulean-blue to-minty-aqua text-white rounded-xl
                         hover:from-minty-aqua hover:to-cerulean-blue disabled:opacity-50
                         disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-cerulean-blue/30
                         flex items-center justify-center min-w-[64px] group hover:scale-105 active:scale-95'
				>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-5 w-5 transition-transform group-hover:translate-x-0.5'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M12 19l9 2-9-18-9 18 9-2zm0 0v-8'
						/>
					</svg>
				</button>
			</div>

			{/* End-to-end encryption notice */}
			<div className='px-4 py-2 text-xs text-zinc-500 text-center'>
				<span className='flex items-center justify-center'>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-3 w-3 mr-1'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={2}
							d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
						/>
					</svg>
					Messages are end-to-end encrypted
				</span>
			</div>
		</div>
	);
};

export default MessageInput;
