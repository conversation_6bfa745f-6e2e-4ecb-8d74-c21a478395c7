import React, { useEffect, useRef } from "react";
import { useAuth } from "../contexts/AuthContextImproved";
import TimeAgo from "./TimeAgo";

interface Message {
	id: string;
	content: string;
	senderId: string;
	timestamp: string;
	encrypted: boolean;
	delivered: boolean;
	read: boolean;
}

interface MessageListProps {
	messages: Message[];
	isLoading?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({ messages, isLoading }) => {
	const { user } = useAuth();
	const bottomRef = useRef<HTMLDivElement>(null);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages]);

	// Group messages by date
	const groupedMessages = messages.reduce((groups, message) => {
		const date = new Date(message.timestamp).toLocaleDateString();
		if (!groups[date]) {
			groups[date] = [];
		}
		groups[date].push(message);
		return groups;
	}, {} as Record<string, Message[]>);

	// Format time in a more readable way
	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
	};

	return (
		<div
			className='flex-1 overflow-y-auto p-6 space-y-8 custom-scrollbar'
			ref={bottomRef}
		>
			{Object.entries(groupedMessages).map(([date, dateMessages]) => (
				<div key={date} className='space-y-6'>
					<div className='flex justify-center'>
						<div className='bg-gradient-to-r from-zinc-800/60 to-zinc-700/60 text-zinc-300 text-xs px-4 py-2 rounded-full border border-zinc-700/30 shadow-lg backdrop-blur-sm'>
							{date === new Date().toLocaleDateString() ? "Today" : date}
						</div>
					</div>

					{dateMessages.map((message, index) => {
						const isSelf = message.senderId === user?.id;
						const prevMessage = dateMessages[index - 1];
						const isConsecutive =
							prevMessage && prevMessage.senderId === message.senderId;

						return (
							<div
								key={message.id}
								className={`flex ${isSelf ? "justify-end" : "justify-start"} ${
									isConsecutive ? "mt-1" : "mt-4"
								}`}
							>
								<div
									className={`max-w-[75%] group relative ${
										isSelf ? "mr-2" : "ml-2"
									}`}
								>
									<div
										className={`message-bubble rounded-2xl px-5 py-3 shadow-enhanced transition-all duration-200 hover:shadow-enhanced-lg interactive-hover ${
											isSelf
												? "bg-gradient-to-br from-emerald-600 to-emerald-700 text-white shadow-emerald-900/30 message-self"
												: "bg-gradient-to-br from-zinc-800 to-zinc-900 text-zinc-100 shadow-black/40 message-other"
										} ${
											!isConsecutive
												? isSelf
													? "rounded-br-md"
													: "rounded-bl-md"
												: ""
										}`}
									>
										<div className='break-words leading-relaxed'>
											{message.content}
										</div>
										<div className='text-xs mt-2 flex items-center gap-2 opacity-75'>
											<TimeAgo
												timestamp={message.timestamp}
												className='font-medium'
											/>
											{message.encrypted && (
												<span
													className='flex items-center gap-1'
													title='End-to-end encrypted'
												>
													<svg
														xmlns='http://www.w3.org/2000/svg'
														className='h-3 w-3'
														fill='none'
														viewBox='0 0 24 24'
														stroke='currentColor'
													>
														<path
															strokeLinecap='round'
															strokeLinejoin='round'
															strokeWidth={2}
															d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
														/>
													</svg>
												</span>
											)}
											{isSelf && (
												<span className='ml-1'>
													{message.read ? (
														<span className='text-emerald-400' title='Read'>
															✓✓
														</span>
													) : message.delivered ? (
														<span className='text-zinc-400' title='Delivered'>
															✓
														</span>
													) : (
														<span className='text-zinc-600' title='Sending...'>
															⌛
														</span>
													)}
												</span>
											)}
										</div>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			))}

			{messages.length === 0 && !isLoading && (
				<div className='h-full flex flex-col items-center justify-center text-center px-4 py-10'>
					<div className='w-16 h-16 bg-zinc-800 rounded-full flex items-center justify-center mb-4'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-8 w-8 text-zinc-500'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={1.5}
								d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
							/>
						</svg>
					</div>
					<h3 className='text-zinc-300 font-medium text-lg'>No messages yet</h3>
					<p className='text-zinc-500 mt-1'>
						Start the conversation by sending a message
					</p>
				</div>
			)}

			{isLoading && (
				<div className='flex justify-center py-2'>
					<div className='flex space-x-1'>
						<div
							className='w-2 h-2 bg-emerald-600 rounded-full animate-bounce'
							style={{ animationDelay: "0ms" }}
						></div>
						<div
							className='w-2 h-2 bg-emerald-600 rounded-full animate-bounce'
							style={{ animationDelay: "150ms" }}
						></div>
						<div
							className='w-2 h-2 bg-emerald-600 rounded-full animate-bounce'
							style={{ animationDelay: "300ms" }}
						></div>
					</div>
				</div>
			)}
			<div ref={messagesEndRef} />
		</div>
	);
};

export default MessageList;
