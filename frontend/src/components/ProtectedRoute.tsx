import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContextImproved";

interface ProtectedRouteProps {
	children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
	const { isAuthenticated, loading } = useAuth();

	if (loading) {
		return <div>Loading...</div>;
	}

	if (!isAuthenticated) {
		return <Navigate to='/' replace />;
	}

	return <>{children}</>;
};

export default ProtectedRoute;
