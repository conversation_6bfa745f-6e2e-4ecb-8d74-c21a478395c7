import React, { useEffect, useRef } from "react";
import SoundToggle from "./SoundToggle";

interface SettingsPanelProps {
	isOpen: boolean;
	onClose: () => void;
	soundsEnabled: boolean;
	onSoundToggle: (enabled: boolean) => void;
	version?: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
	isOpen,
	onClose,
	soundsEnabled,
	onSoundToggle,
	version = "1.0.0",
}) => {
	const panelRef = useRef<HTMLDivElement>(null);

	// Close on escape key
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === "Escape" && isOpen) {
				onClose();
			}
		};

		// Close on click outside
		const handleClickOutside = (e: MouseEvent) => {
			if (
				panelRef.current &&
				!panelRef.current.contains(e.target as Node) &&
				isOpen
			) {
				onClose();
			}
		};

		document.addEventListener("keydown", handleEscape);
		document.addEventListener("mousedown", handleClickOutside);

		// Prevent scrolling when modal is open
		if (isOpen) {
			document.body.style.overflow = "hidden";
		}

		return () => {
			document.removeEventListener("keydown", handleEscape);
			document.removeEventListener("mousedown", handleClickOutside);
			document.body.style.overflow = "auto";
		};
	}, [isOpen, onClose]);

	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center animate-fadeIn'>
			<div
				ref={panelRef}
				className='bg-zinc-900 border border-zinc-800 rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden animate-slideIn'
			>
				<div className='flex justify-between items-center p-4 border-b border-zinc-800'>
					<h2 className='text-xl font-semibold text-white'>Settings</h2>
					<button
						onClick={onClose}
						className='text-zinc-400 hover:text-white transition-colors rounded-full p-1 hover:bg-zinc-800'
						aria-label='Close settings'
					>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M6 18L18 6M6 6l12 12'
							/>
						</svg>
					</button>
				</div>

				<div className='max-h-[70vh] overflow-y-auto p-4'>
					<div className='space-y-4'>
						{/* Notification Settings */}
						<div>
							<h3 className='text-md font-medium text-zinc-300 mb-2 px-2'>
								Notifications
							</h3>
							<div className='bg-zinc-800/50 rounded-lg p-4'>
								<div className='flex items-center justify-between'>
									<div>
										<div className='font-medium text-zinc-200'>
											Sound Notifications
										</div>
										<div className='text-sm text-zinc-400'>
											Play sounds for new messages and typing
										</div>
									</div>
									<SoundToggle
										initialState={soundsEnabled}
										onChange={onSoundToggle}
									/>
								</div>
							</div>
						</div>

						{/* Security Settings */}
						<div>
							<h3 className='text-md font-medium text-zinc-300 mb-2 px-2'>
								Security
							</h3>
							<div className='bg-zinc-800/50 rounded-lg p-4'>
								<div className='flex items-center justify-between'>
									<div>
										<div className='font-medium text-zinc-200'>
											End-to-End Encryption
										</div>
										<div className='text-sm text-zinc-400'>
											All messages are encrypted
										</div>
									</div>
									<div className='bg-emerald-900/30 text-emerald-400 text-xs px-2 py-1 rounded-full border border-emerald-800/50 flex items-center'>
										<span className='w-1.5 h-1.5 bg-emerald-400 rounded-full mr-1'></span>
										Enabled
									</div>
								</div>
							</div>
						</div>

						{/* About Section */}
						<div>
							<h3 className='text-md font-medium text-zinc-300 mb-2 px-2'>
								About
							</h3>
							<div className='bg-zinc-800/50 rounded-lg p-4'>
								<div className='text-sm'>
									<div className='flex items-center mb-2'>
										<svg
											xmlns='http://www.w3.org/2000/svg'
											className='h-5 w-5 text-emerald-500 mr-2'
											fill='none'
											viewBox='0 0 24 24'
											stroke='currentColor'
										>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
											/>
										</svg>
										<span className='text-zinc-200 font-medium'>
											Cypher Chat v{version}
										</span>
									</div>
									<p className='text-zinc-400'>
										A secure, end-to-end encrypted chat application
									</p>
									<div className='mt-3 pt-3 border-t border-zinc-700/50 text-xs text-zinc-500'>
										<p>© 2023 Cypher Chat. All rights reserved.</p>
										<p className='mt-1'>Built with PostgreSQL, Go, and React</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className='p-4 border-t border-zinc-800 flex justify-end bg-zinc-900/80'>
					<button
						onClick={onClose}
						className='px-4 py-2 bg-zinc-800 hover:bg-zinc-700 text-zinc-200 rounded-md transition-colors'
					>
						Close
					</button>
				</div>
			</div>
		</div>
	);
};

export default SettingsPanel;
