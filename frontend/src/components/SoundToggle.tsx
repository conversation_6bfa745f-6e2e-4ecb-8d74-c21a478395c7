import React, { useState, useEffect } from 'react';

interface SoundToggleProps {
  initialState?: boolean;
  onChange?: (enabled: boolean) => void;
}

const SoundToggle: React.FC<SoundToggleProps> = ({ 
  initialState = true,
  onChange 
}) => {
  const [enabled, setEnabled] = useState(initialState);
  
  // Load saved preference from localStorage on mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('soundsEnabled');
    if (savedPreference !== null) {
      const parsedValue = savedPreference === 'true';
      setEnabled(parsedValue);
      if (onChange) onChange(parsedValue);
    }
  }, [onChange]);
  
  const toggleSound = () => {
    const newState = !enabled;
    setEnabled(newState);
    
    // Save preference to localStorage
    localStorage.setItem('soundsEnabled', String(newState));
    
    // Notify parent component
    if (onChange) onChange(newState);
  };
  
  return (
    <button
      onClick={toggleSound}
      className="p-2 rounded-full hover:bg-zinc-800 text-zinc-400 hover:text-white transition-colors"
      title={enabled ? 'Mute notification sounds' : 'Enable notification sounds'}
    >
      {enabled ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.536 8.464a5 5 0 010 7.072M12 6a9 9 0 010 12M8.464 8.464a5 5 0 010 7.072" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
        </svg>
      )}
    </button>
  );
};

export default SoundToggle;
