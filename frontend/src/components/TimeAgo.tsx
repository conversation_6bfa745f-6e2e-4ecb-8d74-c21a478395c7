import React, { useState, useEffect } from 'react';

interface TimeAgoProps {
  timestamp: string;
  className?: string;
}

/**
 * A component that displays a relative time (e.g., "2 minutes ago")
 */
const TimeAgo: React.FC<TimeAgoProps> = ({ timestamp, className = '' }) => {
  const [timeAgo, setTimeAgo] = useState<string>('');
  
  useEffect(() => {
    // Initial calculation
    calculateTimeAgo();
    
    // Update the time ago every minute
    const intervalId = setInterval(calculateTimeAgo, 60000);
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [timestamp]);
  
  const calculateTimeAgo = () => {
    const date = new Date(timestamp);
    const now = new Date();
    const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    // Less than a minute
    if (seconds < 60) {
      setTimeAgo('just now');
      return;
    }
    
    // Less than an hour
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
      setTimeAgo(`${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`);
      return;
    }
    
    // Less than a day
    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
      setTimeAgo(`${hours} ${hours === 1 ? 'hour' : 'hours'} ago`);
      return;
    }
    
    // Less than a week
    const days = Math.floor(hours / 24);
    if (days < 7) {
      setTimeAgo(`${days} ${days === 1 ? 'day' : 'days'} ago`);
      return;
    }
    
    // Format as date
    setTimeAgo(date.toLocaleDateString());
  };
  
  return (
    <span className={className} title={new Date(timestamp).toLocaleString()}>
      {timeAgo}
    </span>
  );
};

export default TimeAgo;
