import React, { useMemo } from "react";

interface UserAvatarProps {
	userId: string;
	username?: string;
	size?: "sm" | "md" | "lg" | "xl";
	showStatus?: boolean;
	isOnline?: boolean;
	className?: string;
	showTooltip?: boolean;
}

/**
 * An enhanced component that displays a user avatar with optional online status indicator
 * and username tooltip
 */
const UserAvatar: React.FC<UserAvatarProps> = ({
	userId,
	username,
	size = "md",
	showStatus = false,
	isOnline = false,
	className = "",
	showTooltip = false,
}) => {
	// Generate a consistent color based on the user ID
	const backgroundColor = useMemo(() => {
		// Simple hash function to generate a number from a string
		const hash = userId.split("").reduce((acc, char) => {
			return char.charCodeAt(0) + ((acc << 5) - acc);
		}, 0);

		// Use the hash to generate a hue value (0-360)
		const hue = Math.abs(hash % 360);

		// Use a fixed saturation and lightness for a consistent look
		return `hsl(${hue}, 70%, 45%)`;
	}, [userId]);

	// Generate a gradient for a more modern look
	const backgroundGradient = useMemo(() => {
		const hue = parseInt(backgroundColor.match(/\d+/)?.[0] || "0");
		const hue2 = (hue + 40) % 360;
		return `linear-gradient(135deg, ${backgroundColor}, hsl(${hue2}, 70%, 35%))`;
	}, [backgroundColor]);

	// Determine size classes
	const sizeClasses = {
		sm: "w-8 h-8 text-xs",
		md: "w-10 h-10 text-sm",
		lg: "w-14 h-14 text-lg",
		xl: "w-20 h-20 text-2xl",
	}[size];

	// Status indicator size
	const statusSize = {
		sm: "w-2.5 h-2.5 -right-0.5 -bottom-0.5",
		md: "w-3.5 h-3.5 -right-1 -bottom-1",
		lg: "w-4 h-4 -right-1 -bottom-1",
		xl: "w-5 h-5 -right-1 -bottom-1",
	}[size];

	// Get initials - use username if provided, otherwise use first 2 chars of userId
	const displayName = username || `User ${userId.slice(0, 8)}`;
	const initials = username
		? username
				.split(" ")
				.map((n) => n[0])
				.join("")
				.toUpperCase()
				.slice(0, 2)
		: userId.slice(0, 2).toUpperCase();

	return (
		<div
			className={`relative group ${className}`}
			title={showTooltip ? displayName : undefined}
		>
			<div
				className={`${sizeClasses} rounded-full flex items-center justify-center font-medium text-white shadow-md transition-transform duration-200 group-hover:scale-105`}
				style={{ background: backgroundGradient }}
			>
				{initials}
			</div>

			{showStatus && (
				<div
					className={`absolute ${statusSize} rounded-full border-2 border-zinc-900 ${
						isOnline ? "bg-emerald-500 animate-pulse" : "bg-zinc-500"
					}`}
				/>
			)}

			{showTooltip && (
				<div className='absolute opacity-0 group-hover:opacity-100 transition-opacity duration-200 bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 text-xs text-white rounded whitespace-nowrap pointer-events-none'>
					{displayName}
				</div>
			)}
		</div>
	);
};

export default UserAvatar;
