import React from 'react';

interface Client {
	id: string;
	publicKey: string;
	isOnline: boolean;
	status: string;
}

interface WelcomeScreenProps {
	clients: Client[];
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ clients }) => {
	return (
		<div className='flex-1 flex flex-col items-center justify-center text-center p-12 relative overflow-hidden'>
			{/* Background decoration */}
			<div className='absolute inset-0 bg-gradient-to-br from-emerald-900/5 via-transparent to-zinc-900/10'></div>
			<div className='absolute top-1/4 left-1/4 w-32 h-32 bg-emerald-500/5 rounded-full blur-3xl'></div>
			<div className='absolute bottom-1/4 right-1/4 w-40 h-40 bg-zinc-500/5 rounded-full blur-3xl'></div>

			<div className='relative z-10'>
				<div className='w-32 h-32 bg-gradient-to-br from-zinc-800/60 to-zinc-900/60 rounded-3xl flex items-center justify-center mb-8 shadow-2xl border border-zinc-700/30'>
					<svg
						xmlns='http://www.w3.org/2000/svg'
						className='h-16 w-16 text-zinc-400'
						fill='none'
						viewBox='0 0 24 24'
						stroke='currentColor'
					>
						<path
							strokeLinecap='round'
							strokeLinejoin='round'
							strokeWidth={1.5}
							d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
						/>
					</svg>
				</div>
				<h2 className='text-2xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent mb-4'>
					Welcome to Cypher Chat
				</h2>
				<p className='text-zinc-400 max-w-lg leading-relaxed mb-6'>
					Select a contact from the sidebar to start a secure, end-to-end
					encrypted conversation. Your messages are protected with military-grade encryption.
				</p>
				{clients.length === 0 && (
					<div className='mt-6 p-4 bg-zinc-800/30 rounded-lg border border-zinc-800 max-w-md'>
						<h3 className='font-medium text-zinc-300 mb-2'>
							No contacts available
						</h3>
						<p className='text-zinc-500 text-sm'>
							Wait for other users to connect or open Cypher Chat in another
							browser window to test the chat functionality.
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default WelcomeScreen;
