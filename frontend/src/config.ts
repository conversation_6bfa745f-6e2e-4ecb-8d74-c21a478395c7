interface Config {
  wsBaseUrl: string;
  performance: {
    reconnectAttempts: number;
    reconnectInterval: number;
    messageBufferSize: number;
    typingDebounceTime: number;
    messageQueueSize: number;
  };
  security: {
    keyRotationInterval: number;
    messageRetentionDays: number;
    maxFileSize: number;
  };
}

const isDevelopment = process.env.NODE_ENV === 'development';

export const config: Config = {
  // WebSocket connection URL
  wsBaseUrl: isDevelopment 
    ? 'ws://localhost:8080/ws'
    : `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`,

  // Performance settings
  performance: {
    reconnectAttempts: 5,
    reconnectInterval: 1000, // Base interval in ms
    messageBufferSize: 100, // Number of messages to keep in memory
    typingDebounceTime: 500, // Time in ms to debounce typing indicator
    messageQueueSize: 100
  },

  // Security settings
  security: {
    keyRotationInterval: 24 * 60 * 60 * 1000, // 24 hours in ms
    messageRetentionDays: 30,
    maxFileSize: 10 * 1024 * 1024, // 10MB in bytes
  },
};

export default config;
