import React, { createContext, useContext, useEffect, useState } from "react";
import authService from "../services/AuthService";
import { User } from "../types/auth";
import logger from "../utils/logger";

interface AuthContextType {
	user: User | null;
	loading: boolean;
	error: Error | null;
	register: (username: string, password: string) => Promise<User>;
	login: (username: string, password: string) => Promise<User>;
	loginWithKey: (encryptedKey: string, password: string) => Promise<User>;
	logout: () => Promise<void>;
	exportKey: (password: string) => Promise<string>;
}

export const AuthContext = createContext<AuthContextType | undefined>(
	undefined
);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
	children,
}) => {
	const [user, setUser] = useState<User | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<Error | null>(null);

	// Try to restore user session on mount
	useEffect(() => {
		const restoreSession = async () => {
			try {
				const restoredUser = await authService.restoreSession();
				setUser(restoredUser);
			} catch (err) {
				logger.error("Failed to restore session:", err);
			} finally {
				setLoading(false);
			}
		};

		restoreSession();
	}, []);

	const register = async (
		username: string,
		password: string
	): Promise<User> => {
		try {
			setLoading(true);
			setError(null);

			const newUser = await authService.register(username, password);
			setUser(newUser);
			return newUser;
		} catch (err) {
			const error =
				err instanceof Error ? err : new Error("Registration failed");
			setError(error);
			throw error;
		} finally {
			setLoading(false);
		}
	};

	const login = async (username: string, password: string): Promise<User> => {
		try {
			setLoading(true);
			setError(null);

			const loggedInUser = await authService.login(username, password);
			setUser(loggedInUser);
			return loggedInUser;
		} catch (err) {
			const error = err instanceof Error ? err : new Error("Login failed");
			setError(error);
			throw error;
		} finally {
			setLoading(false);
		}
	};

	const loginWithKey = async (
		encryptedKey: string,
		password: string
	): Promise<User> => {
		try {
			setLoading(true);
			setError(null);

			const importedUser = await authService.loginWithKey(
				encryptedKey,
				password
			);
			setUser(importedUser);
			return importedUser;
		} catch (err) {
			const error = err instanceof Error ? err : new Error("Key import failed");
			setError(error);
			throw error;
		} finally {
			setLoading(false);
		}
	};

	const logout = async (): Promise<void> => {
		try {
			await authService.logout();
			setUser(null);
		} catch (err) {
			logger.error("Logout error:", err);
			// Still clear the user even if server logout fails
			setUser(null);
		}
	};

	const exportKey = async (password: string): Promise<string> => {
		if (!user) {
			throw new Error("No user logged in");
		}

		try {
			return await authService.exportKey(user, password);
		} catch (err) {
			const error =
				err instanceof Error ? err : new Error("Failed to export key");
			throw error;
		}
	};

	return (
		<AuthContext.Provider
			value={{
				user,
				loading,
				error,
				register,
				login,
				loginWithKey,
				logout,
				exportKey,
			}}
		>
			{children}
		</AuthContext.Provider>
	);
};

export const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};
