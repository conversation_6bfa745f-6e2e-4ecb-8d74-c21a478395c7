import React, { createContext, useContext, useEffect, useReducer, useCallback } from "react";
import { User } from "../types/auth";
import logger from "../utils/logger";
import { generateKeyPair, exportPublicKey } from "../utils/crypto";

// API Configuration
const API_BASE_URL = "http://localhost:8080/api";

// Auth State Types
interface AuthState {
	user: User | null;
	loading: boolean;
	error: string | null;
	isAuthenticated: boolean;
	token: string | null;
	refreshToken: string | null;
}

// Auth Actions
type AuthAction =
	| { type: "SET_LOADING"; payload: boolean }
	| { type: "SET_ERROR"; payload: string | null }
	| { type: "LOGIN_SUCCESS"; payload: { user: User; token: string; refreshToken?: string } }
	| { type: "LOGOUT" }
	| { type: "UPDATE_USER"; payload: User }
	| { type: "CLEAR_ERROR" };

// Initial State
const initialState: AuthState = {
	user: null,
	loading: true,
	error: null,
	isAuthenticated: false,
	token: null,
	refreshToken: null,
};

// Auth Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
	switch (action.type) {
		case "SET_LOADING":
			return { ...state, loading: action.payload };
		case "SET_ERROR":
			return { ...state, error: action.payload, loading: false };
		case "LOGIN_SUCCESS":
			return {
				...state,
				user: action.payload.user,
				token: action.payload.token,
				refreshToken: action.payload.refreshToken || null,
				isAuthenticated: true,
				loading: false,
				error: null,
			};
		case "LOGOUT":
			return {
				...initialState,
				loading: false,
			};
		case "UPDATE_USER":
			return { ...state, user: action.payload };
		case "CLEAR_ERROR":
			return { ...state, error: null };
		default:
			return state;
	}
}

// Context Type
interface AuthContextType {
	user: User | null;
	loading: boolean;
	error: string | null;
	isAuthenticated: boolean;
	register: (username: string, password: string) => Promise<void>;
	login: (username: string, password: string) => Promise<void>;
	logout: () => Promise<void>;
	refreshAuth: () => Promise<void>;
	clearError: () => void;
}

// Create Context
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage Keys
const STORAGE_KEYS = {
	TOKEN: "cypher-chat-token",
	REFRESH_TOKEN: "cypher-chat-refresh-token",
	USER: "cypher-chat-user",
} as const;

// Auth Provider Component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [state, dispatch] = useReducer(authReducer, initialState);

	// API Helper
	const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
		const url = `${API_BASE_URL}${endpoint}`;
		const config: RequestInit = {
			headers: {
				"Content-Type": "application/json",
				...options.headers,
			},
			...options,
		};

		// Add auth token if available
		if (state.token) {
			config.headers = {
				...config.headers,
				Authorization: `Bearer ${state.token}`,
			};
		}

		const response = await fetch(url, config);
		
		if (!response.ok) {
			const errorData = await response.text();
			throw new Error(errorData || `HTTP ${response.status}`);
		}

		return response.json();
	}, [state.token]);

	// Persist auth data to localStorage
	const persistAuthData = useCallback((user: User, token: string, refreshToken?: string) => {
		localStorage.setItem(STORAGE_KEYS.TOKEN, token);
		localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
		if (refreshToken) {
			localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
		}
	}, []);

	// Clear auth data from localStorage
	const clearAuthData = useCallback(() => {
		localStorage.removeItem(STORAGE_KEYS.TOKEN);
		localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
		localStorage.removeItem(STORAGE_KEYS.USER);
	}, []);

	// Register function
	const register = useCallback(async (username: string, password: string) => {
		try {
			dispatch({ type: "SET_LOADING", payload: true });
			dispatch({ type: "CLEAR_ERROR" });

			// Generate key pair client-side
			const keyPair = await generateKeyPair();
			const publicKey = await exportPublicKey(keyPair.publicKey);

			// Call backend registration API
			const response = await apiCall("/auth/register", {
				method: "POST",
				body: JSON.stringify({
					username,
					password,
					public_key: publicKey,
				}),
			});

			// Store the private key securely (in a real app, this would be encrypted)
			const user: User = {
				id: response.user.id,
				username: response.user.username,
				exportedPublicKey: publicKey,
				keyPair: keyPair,
			};

			persistAuthData(user, response.token, response.refresh_token);
			dispatch({
				type: "LOGIN_SUCCESS",
				payload: {
					user,
					token: response.token,
					refreshToken: response.refresh_token,
				},
			});

			logger.info("User registered successfully", { username });
		} catch (error) {
			const message = error instanceof Error ? error.message : "Registration failed";
			dispatch({ type: "SET_ERROR", payload: message });
			logger.error("Registration failed", { error, username });
			throw error;
		}
	}, [apiCall, persistAuthData]);

	// Login function
	const login = useCallback(async (username: string, password: string) => {
		try {
			dispatch({ type: "SET_LOADING", payload: true });
			dispatch({ type: "CLEAR_ERROR" });

			// Call backend login API
			const response = await apiCall("/auth/login", {
				method: "POST",
				body: JSON.stringify({ username, password }),
			});

			// For existing users, we need to restore their key pair
			// In a real implementation, this would involve secure key recovery
			const keyPair = await generateKeyPair(); // Temporary - should restore actual keys
			const publicKey = await exportPublicKey(keyPair.publicKey);

			const user: User = {
				id: response.user.id,
				username: response.user.username,
				exportedPublicKey: publicKey,
				keyPair: keyPair,
			};

			persistAuthData(user, response.token, response.refresh_token);
			dispatch({
				type: "LOGIN_SUCCESS",
				payload: {
					user,
					token: response.token,
					refreshToken: response.refresh_token,
				},
			});

			logger.info("User logged in successfully", { username });
		} catch (error) {
			const message = error instanceof Error ? error.message : "Login failed";
			dispatch({ type: "SET_ERROR", payload: message });
			logger.error("Login failed", { error, username });
			throw error;
		}
	}, [apiCall, persistAuthData]);

	// Logout function
	const logout = useCallback(async () => {
		try {
			// Call backend logout API if token exists
			if (state.token) {
				await apiCall("/auth/logout", { method: "POST" });
			}
		} catch (error) {
			logger.error("Logout API call failed", { error });
			// Continue with local logout even if API call fails
		} finally {
			clearAuthData();
			dispatch({ type: "LOGOUT" });
			logger.info("User logged out");
		}
	}, [state.token, apiCall, clearAuthData]);

	// Refresh authentication
	const refreshAuth = useCallback(async () => {
		try {
			const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
			if (!token) {
				dispatch({ type: "SET_LOADING", payload: false });
				return;
			}

			// Verify token with backend
			const response = await fetch(`${API_BASE_URL}/auth/verify`, {
				headers: { Authorization: `Bearer ${token}` },
			});

			if (!response.ok) {
				throw new Error("Token verification failed");
			}

			const userData = await response.json();
			const storedUser = localStorage.getItem(STORAGE_KEYS.USER);
			
			if (storedUser) {
				const user = JSON.parse(storedUser);
				dispatch({
					type: "LOGIN_SUCCESS",
					payload: { user, token },
				});
			}
		} catch (error) {
			logger.error("Auth refresh failed", { error });
			clearAuthData();
			dispatch({ type: "LOGOUT" });
		}
	}, [clearAuthData]);

	// Clear error
	const clearError = useCallback(() => {
		dispatch({ type: "CLEAR_ERROR" });
	}, []);

	// Initialize auth state on mount
	useEffect(() => {
		refreshAuth();
	}, [refreshAuth]);

	// Auto-refresh token periodically
	useEffect(() => {
		if (!state.isAuthenticated) return;

		const interval = setInterval(() => {
			refreshAuth();
		}, 15 * 60 * 1000); // Refresh every 15 minutes

		return () => clearInterval(interval);
	}, [state.isAuthenticated, refreshAuth]);

	const contextValue: AuthContextType = {
		user: state.user,
		loading: state.loading,
		error: state.error,
		isAuthenticated: state.isAuthenticated,
		register,
		login,
		logout,
		refreshAuth,
		clearError,
	};

	return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};
