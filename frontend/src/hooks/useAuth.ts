import { useCallback, useEffect, useState } from "react";
import {
	exportPublicKey,
	generateKeyPair,
	importKeyPair,
	validateKeyData,
} from "../utils/crypto";
import logger from "../utils/logger";

interface User {
	id: string;
	privateKey: CryptoKey;
	exportedPublicKey: string;
}

interface AuthHook {
	user: User | null;
	loading: boolean;
	login: () => Promise<User>;
	loginWithKey: (keyData: string) => Promise<User>;
}

export const useAuth = (): AuthHook => {
	const [user, setUser] = useState<User | null>(() => {
		// Try to restore user from session storage
		const savedUser = sessionStorage.getItem("user");
		return savedUser ? JSON.parse(savedUser) : null;
	});
	const [loading, setLoading] = useState(!user);

	// Save user to session storage when it changes
	useEffect(() => {
		if (user) {
			sessionStorage.setItem("user", JSON.stringify(user));
		} else {
			sessionStorage.removeItem("user");
		}
	}, [user]);

	const login = useCallback(async () => {
		try {
			setLoading(true);
			const keyPair = await generateKeyPair();
			const exportedPublicKey = await exportPublicKey(keyPair.publicKey);
			const userId = crypto.randomUUID();

			const userData = {
				id: userId,
				privateKey: keyPair.privateKey,
				exportedPublicKey,
			};

			setUser(userData);
			return userData;
		} catch (error) {
			logger.error("Login failed", { error });
			throw error;
		} finally {
			setLoading(false);
		}
	}, []);

	const loginWithKey = useCallback(async (keyData: string) => {
		try {
			setLoading(true);
			const { id, exportedPublicKey } = validateKeyData(keyData);
			const keyPair = await importKeyPair(exportedPublicKey);

			const userData = {
				id,
				privateKey: keyPair.privateKey,
				exportedPublicKey,
			};

			setUser(userData);
			return userData;
		} catch (error) {
			logger.error("Login with key failed", { error });
			throw error;
		} finally {
			setLoading(false);
		}
	}, []);

	return { user, loading, login, loginWithKey };
};

export default useAuth;
