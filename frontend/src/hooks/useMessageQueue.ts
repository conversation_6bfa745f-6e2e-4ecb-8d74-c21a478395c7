import { useState, useRef, useCallback } from 'react';
import logger from '../utils/logger';

interface MessageQueue {
    addMessage: (message: string) => void;
    retryFailedMessages: () => void;
    clearQueue: () => void;
    queueSize: number;
}

export const useMessageQueue = (sendMessage: (message: string) => Promise<void>): MessageQueue => {
    const [queueSize, setQueueSize] = useState(0);
    const messageQueue = useRef<string[]>([]);
    const processingRef = useRef(false);

    const processQueue = useCallback(async () => {
        if (processingRef.current || messageQueue.current.length === 0) return;
        processingRef.current = true;

        try {
            while (messageQueue.current.length > 0) {
                const message = messageQueue.current[0];
                await sendMessage(message);
                messageQueue.current.shift();
                setQueueSize(prev => prev - 1);
            }
        } catch (error) {
            logger.error('Failed to process message queue', { error });
        } finally {
            processingRef.current = false;
        }
    }, [sendMessage]);

    const addMessage = useCallback((message: string) => {
        messageQueue.current.push(message);
        setQueueSize(prev => prev + 1);
        processQueue();
    }, [processQueue]);

    const retryFailedMessages = useCallback(() => {
        if (messageQueue.current.length > 0) {
            processQueue();
        }
    }, [processQueue]);

    const clearQueue = useCallback(() => {
        messageQueue.current = [];
        setQueueSize(0);
    }, []);

    return {
        addMessage,
        retryFailedMessages,
        clearQueue,
        queueSize
    };
}; 