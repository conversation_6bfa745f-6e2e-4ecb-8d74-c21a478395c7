import { useCallback, useEffect, useState } from "react";
import { useAuth } from "../contexts/AuthContextImproved";
import logger from "../utils/logger";

const WS_URL = "ws://localhost:8080/ws";

// Connection configuration
const CONNECTION_CONFIG = {
	maxReconnectAttempts: 10,
	initialReconnectDelay: 1000, // 1 second
	maxReconnectDelay: 30000, // 30 seconds
	heartbeatInterval: 30000, // 30 seconds
	connectionTimeout: 10000, // 10 seconds
};

export interface UseWebSocketReturn {
	sendMessage: (message: object) => void;
	lastMessage: WebSocketEventMap["message"] | null;
	connectionStatus:
		| "Connecting"
		| "Connected"
		| "Disconnected"
		| "Error"
		| "Reconnecting";
	reconnect: () => void;
	isConnected: boolean;
	reconnectAttempts: number;
}

const useWebSocket = (): UseWebSocketReturn => {
	const { user } = useAuth();
	const [ws, setWs] = useState<WebSocket | null>(null);
	const [lastMessage, setLastMessage] = useState<
		WebSocketEventMap["message"] | null
	>(null);
	const [connectionStatus, setConnectionStatus] =
		useState<UseWebSocketReturn["connectionStatus"]>("Disconnected");
	const [reconnectAttempts, setReconnectAttempts] = useState(0);

	// Use refs to avoid stale closures in timers
	const wsRef = { current: null as WebSocket | null };
	const reconnectTimeoutRef = { current: null as NodeJS.Timeout | null };
	const heartbeatIntervalRef = { current: null as NodeJS.Timeout | null };
	const isConnectingRef = { current: false };

	const connect = useCallback(() => {
		if (!user) return;

		try {
			// Include user ID in the URL for immediate identification
			const socket = new WebSocket(`${WS_URL}?userId=${user.id}`);

			socket.onopen = () => {
				setConnectionStatus("Connected");
				logger.info("WebSocket connected");

				// Send public key in the format the server expects
				socket.send(
					JSON.stringify({
						type: "public_key",
						content: {
							key: user.exportedPublicKey,
						},
					})
				);

				// Also store the user ID in the URL for identification
				const urlParams = new URLSearchParams(window.location.search);
				urlParams.set("userId", user.id);
				window.history.replaceState(
					{},
					"",
					`${window.location.pathname}?${urlParams}`
				);

				// The server doesn't have a specific message type for requesting client list
				// It broadcasts the client list after each client registration
				// So we don't need to send a specific request
			};

			socket.onmessage = (event) => {
				setLastMessage(event);
				logger.debug("WebSocket message received", { data: event.data });
			};

			socket.onclose = () => {
				setConnectionStatus("Disconnected");
				logger.info("WebSocket disconnected");
			};

			socket.onerror = (error) => {
				setConnectionStatus("Error");
				logger.error("WebSocket error", { error });
			};

			setWs(socket);
		} catch (error) {
			setConnectionStatus("Error");
			logger.error("WebSocket connection failed", { error });
		}
	}, [user]);

	// Connect on mount or user change
	useEffect(() => {
		connect();
		return () => ws?.close();
	}, [connect]);

	// Reconnection handler
	const reconnect = useCallback(() => {
		ws?.close();
		connect();
	}, [connect, ws]);

	// Message sender
	const sendMessage = useCallback(
		(message: object) => {
			if (ws?.readyState === WebSocket.OPEN) {
				ws.send(JSON.stringify(message));
			} else {
				logger.error("WebSocket not connected");
			}
		},
		[ws]
	);

	return {
		sendMessage,
		lastMessage,
		connectionStatus,
		reconnect,
	};
};

export default useWebSocket;
