import { useCallback, useEffect, useRef, useState } from "react";
import { useAuth } from "../contexts/AuthContext";
import logger from "../utils/logger";

const WS_URL = "ws://localhost:8080/ws";

// Connection configuration
const CONNECTION_CONFIG = {
	maxReconnectAttempts: 10,
	initialReconnectDelay: 1000, // 1 second
	maxReconnectDelay: 30000, // 30 seconds
	heartbeatInterval: 30000, // 30 seconds
	connectionTimeout: 10000, // 10 seconds
};

export interface UseWebSocketReturn {
	sendMessage: (message: object) => void;
	lastMessage: WebSocketEventMap["message"] | null;
	connectionStatus:
		| "Connecting"
		| "Connected"
		| "Disconnected"
		| "Error"
		| "Reconnecting";
	reconnect: () => void;
	isConnected: boolean;
	reconnectAttempts: number;
}

const useWebSocketImproved = (): UseWebSocketReturn => {
	const { user } = useAuth();
	const [lastMessage, setLastMessage] = useState<
		WebSocketEventMap["message"] | null
	>(null);
	const [connectionStatus, setConnectionStatus] =
		useState<UseWebSocketReturn["connectionStatus"]>("Disconnected");
	const [reconnectAttempts, setReconnectAttempts] = useState(0);

	// Use refs to avoid stale closures in timers
	const wsRef = useRef<WebSocket | null>(null);
	const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const isConnectingRef = useRef(false);
	const shouldReconnectRef = useRef(true);

	// Calculate exponential backoff delay
	const getReconnectDelay = useCallback((attempt: number): number => {
		const delay = Math.min(
			CONNECTION_CONFIG.initialReconnectDelay * Math.pow(2, attempt),
			CONNECTION_CONFIG.maxReconnectDelay
		);
		// Add jitter to prevent thundering herd
		return delay + Math.random() * 1000;
	}, []);

	// Clear all timers
	const clearTimers = useCallback(() => {
		if (reconnectTimeoutRef.current) {
			clearTimeout(reconnectTimeoutRef.current);
			reconnectTimeoutRef.current = null;
		}
		if (heartbeatIntervalRef.current) {
			clearInterval(heartbeatIntervalRef.current);
			heartbeatIntervalRef.current = null;
		}
	}, []);

	// Setup heartbeat to detect dead connections
	const setupHeartbeat = useCallback(() => {
		clearTimers();
		heartbeatIntervalRef.current = setInterval(() => {
			if (wsRef.current?.readyState === WebSocket.OPEN) {
				wsRef.current.send(JSON.stringify({ type: "ping" }));
			}
		}, CONNECTION_CONFIG.heartbeatInterval);
	}, [clearTimers]);

	// Handle successful connection
	const handleConnectionSuccess = useCallback(() => {
		setConnectionStatus("Connected");
		setReconnectAttempts(0);
		isConnectingRef.current = false;
		logger.info("WebSocket connected successfully");

		// Send public key in the format the server expects
		if (user?.exportedPublicKey && wsRef.current) {
			wsRef.current.send(
				JSON.stringify({
					type: "public_key",
					content: {
						key: user.exportedPublicKey,
					},
				})
			);
		}

		// Setup heartbeat
		setupHeartbeat();
	}, [user, setupHeartbeat]);

	// Handle connection failure and trigger reconnection
	const handleConnectionFailure = useCallback(
		(error?: Event) => {
			isConnectingRef.current = false;
			clearTimers();

			if (!shouldReconnectRef.current) {
				setConnectionStatus("Disconnected");
				return;
			}

			const currentAttempts = reconnectAttempts;
			
			if (currentAttempts >= CONNECTION_CONFIG.maxReconnectAttempts) {
				setConnectionStatus("Error");
				logger.error("Max reconnection attempts reached");
				return;
			}

			setConnectionStatus("Reconnecting");
			setReconnectAttempts(prev => prev + 1);

			const delay = getReconnectDelay(currentAttempts);
			logger.info(`Reconnecting in ${delay}ms (attempt ${currentAttempts + 1})`);

			reconnectTimeoutRef.current = setTimeout(() => {
				if (shouldReconnectRef.current) {
					connect();
				}
			}, delay);
		},
		[reconnectAttempts, getReconnectDelay]
	);

	// Main connection function
	const connect = useCallback(() => {
		if (!user || isConnectingRef.current) return;

		// Close existing connection
		if (wsRef.current) {
			wsRef.current.close();
			wsRef.current = null;
		}

		isConnectingRef.current = true;
		setConnectionStatus("Connecting");

		try {
			const socket = new WebSocket(`${WS_URL}?userId=${user.id}`);
			wsRef.current = socket;

			// Connection timeout
			const connectionTimeout = setTimeout(() => {
				if (socket.readyState === WebSocket.CONNECTING) {
					socket.close();
					handleConnectionFailure();
				}
			}, CONNECTION_CONFIG.connectionTimeout);

			socket.onopen = () => {
				clearTimeout(connectionTimeout);
				handleConnectionSuccess();
			};

			socket.onmessage = (event) => {
				setLastMessage(event);
				logger.debug("WebSocket message received", { data: event.data });
				
				// Handle pong responses
				try {
					const message = JSON.parse(event.data);
					if (message.type === "pong") {
						// Connection is alive, no action needed
						return;
					}
				} catch (e) {
					// Not JSON, ignore
				}
			};

			socket.onclose = (event) => {
				clearTimeout(connectionTimeout);
				logger.info("WebSocket disconnected", { 
					code: event.code, 
					reason: event.reason 
				});
				
				// Don't reconnect if it was a clean close
				if (event.code === 1000) {
					shouldReconnectRef.current = false;
					setConnectionStatus("Disconnected");
					isConnectingRef.current = false;
				} else {
					handleConnectionFailure(event);
				}
			};

			socket.onerror = (error) => {
				clearTimeout(connectionTimeout);
				logger.error("WebSocket error", { error });
				handleConnectionFailure(error);
			};

		} catch (error) {
			logger.error("WebSocket connection failed", { error });
			handleConnectionFailure();
		}
	}, [user, handleConnectionSuccess, handleConnectionFailure]);

	// Manual reconnection
	const reconnect = useCallback(() => {
		shouldReconnectRef.current = true;
		setReconnectAttempts(0);
		clearTimers();
		connect();
	}, [connect, clearTimers]);

	// Message sender with queue support
	const sendMessage = useCallback((message: object) => {
		if (wsRef.current?.readyState === WebSocket.OPEN) {
			wsRef.current.send(JSON.stringify(message));
		} else {
			logger.error("WebSocket not connected, message not sent", { message });
			// Could implement message queuing here
		}
	}, []);

	// Connect on mount or user change
	useEffect(() => {
		if (user) {
			shouldReconnectRef.current = true;
			connect();
		}

		return () => {
			shouldReconnectRef.current = false;
			clearTimers();
			if (wsRef.current) {
				wsRef.current.close(1000, "Component unmounting");
				wsRef.current = null;
			}
		};
	}, [user, connect, clearTimers]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			shouldReconnectRef.current = false;
			clearTimers();
		};
	}, [clearTimers]);

	return {
		sendMessage,
		lastMessage,
		connectionStatus,
		reconnect,
		isConnected: connectionStatus === "Connected",
		reconnectAttempts,
	};
};

export default useWebSocketImproved;
