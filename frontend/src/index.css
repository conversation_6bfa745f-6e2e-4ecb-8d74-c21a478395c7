/* Import enhanced UI styles */
@import './styles/enhanced-ui.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

.retro-terminal {
  @apply bg-black text-green-400 font-mono;
}

.terminal-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.terminal-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

.terminal-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-green-600;
}

.terminal-button {
  @apply bg-terminal-accent text-terminal-bg px-4 py-2 rounded
         hover:bg-terminal-accent-hover transition-colors duration-200
         disabled:opacity-50 disabled:cursor-not-allowed
         focus:outline-none focus:ring-2 focus:ring-terminal-accent focus:ring-offset-2 focus:ring-offset-terminal-bg;
}

.glitch {
  @apply text-2xl font-bold mb-4;
  text-shadow: 
    2px 2px #00ff00,
    -2px -2px #00cc00;
  animation: glitch 1s infinite;
}

.cursor-blink {
  @apply text-2xl;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes glitch {
  0% { text-shadow: 2px 2px #00ff00, -2px -2px #00cc00; }
  25% { text-shadow: -2px 2px #00ff00, 2px -2px #00cc00; }
  50% { text-shadow: 2px -2px #00ff00, -2px 2px #00cc00; }
  75% { text-shadow: -2px -2px #00ff00, 2px 2px #00cc00; }
  100% { text-shadow: 2px 2px #00ff00, -2px -2px #00cc00; }
}
