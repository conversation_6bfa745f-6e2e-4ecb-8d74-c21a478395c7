import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ChatHeader from "../components/ChatHeader";
import ChatMainArea from "../components/ChatMainArea";
import ChatSidebar from "../components/ChatSidebar";
import LoadingSpinner from "../components/LoadingSpinner";
import SettingsPanel from "../components/SettingsPanel";
import WelcomeScreen from "../components/WelcomeScreen";
import { useAuth } from "../contexts/AuthContextImproved";
import useWebSocketImproved from "../hooks/useWebSocketImproved";
import { decryptMessage, encryptMessage } from "../utils/crypto";
import logger from "../utils/logger";

interface Message {
	id: string;
	content: string;
	senderId: string;
	timestamp: string;
	encrypted: boolean;
	delivered: boolean;
	read: boolean;
}

interface Client {
	id: string;
	publicKey: string;
	isOnline: boolean;
	status: string;
}

interface ServerClient {
	id: string;
	public_key: string;
	is_online: boolean;
	status: string;
}

const ChatPage: React.FC = () => {
	const { user, logout } = useAuth();
	const navigate = useNavigate();
	const [messages, setMessages] = useState<Message[]>([]);
	const [clients, setClients] = useState<Client[]>([]);
	const [selectedClient, setSelectedClient] = useState<Client | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSending, setIsSending] = useState(false);
	// Typing indicator states
	const [typingTimeout, setTypingTimeout] = useState<number | null>(null);
	const [remoteTyping, setRemoteTyping] = useState<{ [key: string]: boolean }>(
		{}
	);
	// Sound settings
	const [soundsEnabled, setSoundsEnabled] = useState(true);
	// Settings panel
	const [isSettingsOpen, setIsSettingsOpen] = useState(false);
	// Mobile menu state
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

	const { sendMessage, lastMessage, connectionStatus, reconnect } =
		useWebSocketImproved();

	// Handle incoming messages
	useEffect(() => {
		if (!lastMessage || !user) return;

		const handleMessage = async () => {
			try {
				const data = JSON.parse(lastMessage.data);

				switch (data.type) {
					case "client_list":
						// Log the raw client list data for debugging
						console.log("Received client list:", data);

						// Handle the case where clients might be null
						if (data.clients) {
							const clientList = Array.isArray(data.clients)
								? data.clients
								: [];

							console.log("Client list array:", clientList);

							const mappedClients = clientList
								.map((client: ServerClient) => ({
									id: client.id,
									publicKey: client.public_key,
									isOnline: client.is_online,
									status: client.status || "online",
								}))
								.filter((client: Client) => client.id !== user.id);

							console.log("Filtered clients:", mappedClients);
							setClients(mappedClients);
						} else {
							// If no clients, set empty array
							console.log("No clients in message");
							setClients([]);
						}
						setIsLoading(false);
						break;

					case "chat":
						if (!user.privateKey) return;
						setIsSending(true);
						try {
							const decrypted = await decryptMessage(
								data.content as string,
								user.privateKey
							);
							const newMessage: Message = {
								id: crypto.randomUUID(),
								content: decrypted,
								senderId: data.sender_id ?? "unknown",
								timestamp: data.timestamp ?? new Date().toISOString(),
								encrypted: true,
								delivered: true,
								read: false,
							};
							setMessages((prev) => [...prev, newMessage]);

							// Play notification sound for new messages
							if (soundsEnabled) {
								try {
									const messageSound = new Audio(
										"https://cdn.pixabay.com/download/audio/2021/08/04/audio_0625c1539c.mp3"
									);
									messageSound.volume = 0.5;
									if (document.visibilityState !== "visible") {
										messageSound.play().catch(() => {
											// Silently fail - sound is non-critical
										});
									}
								} catch {
									// Removed unused variable
									// Silently fail - sound is non-critical
								}
							}

							// Send delivery receipt
							sendMessage({
								type: "delivery_receipt",
								content: {
									message_id: newMessage.id,
									status: "delivered",
									recipient_id: data.sender_id,
								},
							});
						} catch (error) {
							logger.error("Failed to decrypt message", { error });
						} finally {
							setIsSending(false);
						}
						break;

					case "typing":
						// Update typing indicator for the sender
						if (data.sender_id) {
							const isTyping = data.content?.is_typing === true;

							// Only play sound when typing starts, not when it stops
							if (isTyping && !remoteTyping[data.sender_id] && soundsEnabled) {
								// Play typing sound if the window is visible
								try {
									const typingSound = new Audio(
										"https://cdn.pixabay.com/download/audio/2022/03/10/audio_270f49d5bf.mp3"
									);
									typingSound.volume = 0.3;
									if (document.visibilityState === "visible") {
										typingSound.play().catch(() => {
											// Silently fail - sound is non-critical
										});
									}
								} catch {
									// Removed unused variable
									// Silently fail - sound is non-critical
								}
							}

							setRemoteTyping((prev) => ({
								...prev,
								[data.sender_id]: isTyping,
							}));

							// Auto-clear typing indicator after 3 seconds of inactivity
							if (isTyping) {
								setTimeout(() => {
									setRemoteTyping((prev) => ({
										...prev,
										[data.sender_id]: false,
									}));
								}, 3000);
							}
						}
						break;

					case "delivery_receipt":
						// Update message delivery status
						try {
							const receipt = data.content as {
								message_id: string;
								status: string;
							};
							if (receipt && receipt.message_id) {
								setMessages((prev) =>
									prev.map((msg) => {
										if (msg.id === receipt.message_id) {
											return {
												...msg,
												delivered: true,
												read: receipt.status === "read",
											};
										}
										return msg;
									})
								);
							}
						} catch (error) {
							logger.error("Failed to process delivery receipt", { error });
						}
						break;
				}
			} catch (error) {
				logger.error("Failed to process message", { error });
				setIsLoading(false);
			}
		};

		handleMessage();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lastMessage, user]);

	const handleSendMessage = async (content: string) => {
		if (!selectedClient || !user) return;

		try {
			setIsSending(true);
			const encrypted = await encryptMessage(content, selectedClient.publicKey);

			// Generate a unique message ID
			const messageId = crypto.randomUUID();

			sendMessage({
				type: "chat",
				content: encrypted,
				recipient_id: selectedClient.id,
				timestamp: new Date().toISOString(),
				message_id: messageId,
			});

			// Add message to local state
			const newMessage: Message = {
				id: messageId,
				content,
				senderId: user.id,
				timestamp: new Date().toISOString(),
				encrypted: true,
				delivered: false,
				read: false,
			};
			setMessages((prev) => [...prev, newMessage]);

			// Clear typing indicator when sending a message
			if (typingTimeout) {
				clearTimeout(typingTimeout);
				setTypingTimeout(null);
			}

			// Notify the recipient that we stopped typing
			if (selectedClient) {
				sendMessage({
					type: "typing",
					content: {
						recipient_id: selectedClient.id,
						is_typing: false,
					},
				});
			}
		} catch (error) {
			logger.error("Failed to send message", { error });
		} finally {
			setIsSending(false);
		}
	};

	const handleLogout = () => {
		logout();
		navigate("/");
	};

	const handleSoundToggle = (enabled: boolean) => {
		setSoundsEnabled(enabled);
		// Save preference to localStorage
		localStorage.setItem("soundsEnabled", String(enabled));
	};

	if (isLoading) {
		return (
			<div className='flex h-screen items-center justify-center bg-zinc-900'>
				<LoadingSpinner />
			</div>
		);
	}

	return (
		<div className='flex h-screen bg-gradient-to-br from-zinc-900 via-zinc-900 to-zinc-800 text-white overflow-hidden'>
			{/* Mobile overlay */}
			{isMobileMenuOpen && (
				<div
					className='fixed inset-0 bg-black/50 z-40 md:hidden'
					onClick={() => setIsMobileMenuOpen(false)}
				/>
			)}

			{/* Sidebar */}
			<ChatSidebar
				clients={clients}
				selectedClient={selectedClient}
				onClientSelect={(client) => {
					setSelectedClient(client);
					setIsMobileMenuOpen(false);
				}}
				onLogout={handleLogout}
				connectionStatus={connectionStatus}
				onReconnect={reconnect}
				isMobileMenuOpen={isMobileMenuOpen}
				onMobileMenuClose={() => setIsMobileMenuOpen(false)}
			/>

			{/* Main Chat Area */}
			<div className='flex-1 flex flex-col bg-gradient-to-b from-zinc-950 to-zinc-900 relative'>
				{selectedClient ? (
					<>
						<ChatHeader
							selectedClient={selectedClient}
							remoteTyping={remoteTyping}
							soundsEnabled={soundsEnabled}
							onSoundToggle={handleSoundToggle}
							onSettingsOpen={() => setIsSettingsOpen(true)}
							onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
							isMobileMenuOpen={isMobileMenuOpen}
						/>

						<ChatMainArea
							messages={messages}
							isLoading={isLoading}
							isSending={isSending}
							onSendMessage={handleSendMessage}
							onTyping={(isTyping: boolean) => {
								if (selectedClient) {
									sendMessage({
										type: "typing",
										content: {
											recipient_id: selectedClient.id,
											is_typing: isTyping,
										},
									});
								}
							}}
						/>
					</>
				) : (
					<WelcomeScreen clients={clients} />
				)}
			</div>

			{/* Settings Panel */}
			<SettingsPanel
				isOpen={isSettingsOpen}
				onClose={() => setIsSettingsOpen(false)}
				soundsEnabled={soundsEnabled}
				onSoundToggle={handleSoundToggle}
				version='1.0.0'
			/>
		</div>
	);
};

export default ChatPage;
