import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContextImproved";

enum AuthMode {
	LOGIN = "login",
	REGISTER = "register",
	IMPORT_KEY = "import_key",
	EXPORT_KEY = "export_key",
}

const LoginPage: React.FC = () => {
	const { user, register, login, loginWithKey, exportKey } = useAuth();
	const navigate = useNavigate();

	const [mode, setMode] = useState<AuthMode>(AuthMode.LOGIN);
	const [username, setUsername] = useState("");
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [importedKey, setImportedKey] = useState("");
	const [importPassword, setImportPassword] = useState("");
	const [exportPassword, setExportPassword] = useState("");
	const [exportedKey, setExportedKey] = useState("");
	const [error, setError] = useState<string | null>(null);
	const [success, setSuccess] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setSuccess(null);

		if (!username || !password) {
			setError("Please enter both username and password");
			return;
		}

		try {
			setIsLoading(true);
			await login(username, password);
			navigate("/chat");
		} catch (err) {
			setError(err instanceof Error ? err.message : "Login failed");
		} finally {
			setIsLoading(false);
		}
	};

	const handleRegister = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setSuccess(null);

		if (!username || !password) {
			setError("Please enter both username and password");
			return;
		}

		if (password !== confirmPassword) {
			setError("Passwords do not match");
			return;
		}

		try {
			setIsLoading(true);
			await register(username, password);
			setSuccess("Registration successful! You can now proceed to chat.");
			setTimeout(() => {
				navigate("/chat");
			}, 2000);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Registration failed");
		} finally {
			setIsLoading(false);
		}
	};

	const handleImportKey = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setSuccess(null);

		if (!importedKey || !importPassword) {
			setError("Please enter both key data and password");
			return;
		}

		try {
			setIsLoading(true);
			await loginWithKey(importedKey, importPassword);
			navigate("/chat");
		} catch (err) {
			setError(err instanceof Error ? err.message : "Key import failed");
		} finally {
			setIsLoading(false);
		}
	};

	const handleExportKey = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);
		setSuccess(null);

		if (!exportPassword) {
			setError("Please enter a password to protect your key");
			return;
		}

		try {
			setIsLoading(true);
			const keyData = await exportKey(exportPassword);
			setExportedKey(keyData);
			setSuccess(
				"Key exported successfully! Save this key in a secure location."
			);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Key export failed");
		} finally {
			setIsLoading(false);
		}
	};

	const copyToClipboard = async () => {
		try {
			await navigator.clipboard.writeText(exportedKey);
			setSuccess("Key copied to clipboard!");
		} catch (err) {
			setError("Failed to copy to clipboard");
		}
	};

	return (
		<div className='min-h-screen bg-gradient-to-br from-ivory-cream via-baby-blue to-light-sage-green flex items-center justify-center p-4 relative overflow-hidden'>
			{/* Background decoration */}
			<div className='absolute inset-0 bg-gradient-to-br from-cerulean-blue/10 via-transparent to-minty-aqua/10'></div>
			<div className='absolute top-1/4 left-1/4 w-64 h-64 bg-cerulean-blue/10 rounded-full blur-3xl'></div>
			<div className='absolute bottom-1/4 right-1/4 w-80 h-80 bg-minty-aqua/10 rounded-full blur-3xl'></div>

			<div className='w-full max-w-md bg-ivory-cream/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-cerulean-blue/20 p-8 relative z-10 transform transition-all duration-300 hover:shadow-3xl hover:shadow-cerulean-blue/20'>
				{/* Header with logo */}
				<div className='text-center mb-8'>
					<div className='w-16 h-16 bg-gradient-to-br from-cerulean-blue to-minty-aqua rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg shadow-cerulean-blue/30'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-8 w-8 text-white'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
							/>
						</svg>
					</div>
					<h1 className='text-3xl font-bold bg-gradient-to-r from-cerulean-blue to-minty-aqua bg-clip-text text-transparent mb-2'>
						Cypher Chat
					</h1>
					<p className='text-zinc-400 text-sm'>
						Secure End-to-End Encrypted Messaging
					</p>
				</div>

				{/* Mode Selector */}
				<div className='flex mb-8 bg-zinc-700/60 backdrop-blur-sm rounded-xl p-1.5 border border-zinc-600/30'>
					<button
						onClick={() => setMode(AuthMode.LOGIN)}
						className={`flex-1 py-3 px-4 rounded-lg transition-all duration-200 text-sm font-medium relative ${
							mode === AuthMode.LOGIN
								? "bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg shadow-emerald-900/30"
								: "text-zinc-300 hover:bg-zinc-600/50 hover:text-white"
						}`}
					>
						Login
					</button>
					<button
						onClick={() => setMode(AuthMode.REGISTER)}
						className={`flex-1 py-3 px-4 rounded-lg transition-all duration-200 text-sm font-medium relative ${
							mode === AuthMode.REGISTER
								? "bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg shadow-emerald-900/30"
								: "text-zinc-300 hover:bg-zinc-600/50 hover:text-white"
						}`}
					>
						Register
					</button>
					<button
						onClick={() => setMode(AuthMode.IMPORT_KEY)}
						className={`flex-1 py-3 px-2 rounded-lg transition-all duration-200 text-xs font-medium relative ${
							mode === AuthMode.IMPORT_KEY
								? "bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg shadow-emerald-900/30"
								: "text-zinc-300 hover:bg-zinc-600/50 hover:text-white"
						}`}
					>
						Import
					</button>
					{user && (
						<button
							onClick={() => setMode(AuthMode.EXPORT_KEY)}
							className={`flex-1 py-3 px-2 rounded-lg transition-all duration-200 text-xs font-medium relative ${
								mode === AuthMode.EXPORT_KEY
									? "bg-gradient-to-r from-emerald-600 to-emerald-700 text-white shadow-lg shadow-emerald-900/30"
									: "text-zinc-300 hover:bg-zinc-600/50 hover:text-white"
							}`}
						>
							Export
						</button>
					)}
				</div>

				{/* Error/Success Messages */}
				{error && (
					<div className='mb-6 p-4 bg-red-900/30 backdrop-blur-sm border border-red-700/50 text-red-200 rounded-xl flex items-center gap-3 shadow-lg'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5 text-red-400 flex-shrink-0'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
							/>
						</svg>
						<span className='text-sm'>{error}</span>
					</div>
				)}

				{success && (
					<div className='mb-6 p-4 bg-emerald-900/30 backdrop-blur-sm border border-emerald-700/50 text-emerald-200 rounded-xl flex items-center gap-3 shadow-lg'>
						<svg
							xmlns='http://www.w3.org/2000/svg'
							className='h-5 w-5 text-emerald-400 flex-shrink-0'
							fill='none'
							viewBox='0 0 24 24'
							stroke='currentColor'
						>
							<path
								strokeLinecap='round'
								strokeLinejoin='round'
								strokeWidth={2}
								d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
							/>
						</svg>
						<span className='text-sm'>{success}</span>
					</div>
				)}

				{/* Login Form */}
				{mode === AuthMode.LOGIN && (
					<form onSubmit={handleLogin} className='space-y-6'>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Username
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
										/>
									</svg>
								</div>
								<input
									type='text'
									value={username}
									onChange={(e) => setUsername(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Enter your username'
								/>
							</div>
						</div>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Password
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
										/>
									</svg>
								</div>
								<input
									type='password'
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Enter your password'
								/>
							</div>
						</div>
						<button
							type='submit'
							disabled={isLoading}
							className='w-full py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-emerald-900/30 font-medium'
						>
							{isLoading ? (
								<div className='flex items-center justify-center gap-2'>
									<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
									Logging in...
								</div>
							) : (
								"Login"
							)}
						</button>
					</form>
				)}

				{/* Register Form */}
				{mode === AuthMode.REGISTER && (
					<form onSubmit={handleRegister} className='space-y-6'>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Username
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
										/>
									</svg>
								</div>
								<input
									type='text'
									value={username}
									onChange={(e) => setUsername(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Choose a username'
								/>
							</div>
						</div>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Password
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
										/>
									</svg>
								</div>
								<input
									type='password'
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Choose a password'
								/>
							</div>
						</div>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Confirm Password
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
										/>
									</svg>
								</div>
								<input
									type='password'
									value={confirmPassword}
									onChange={(e) => setConfirmPassword(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Confirm your password'
								/>
							</div>
						</div>
						<button
							type='submit'
							disabled={isLoading}
							className='w-full py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-emerald-900/30 font-medium'
						>
							{isLoading ? (
								<div className='flex items-center justify-center gap-2'>
									<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
									Registering...
								</div>
							) : (
								"Register"
							)}
						</button>
					</form>
				)}

				{/* Import Key Form */}
				{mode === AuthMode.IMPORT_KEY && (
					<form onSubmit={handleImportKey} className='space-y-6'>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Key Data
							</label>
							<div className='relative'>
								<div className='absolute top-3 left-3 pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
										/>
									</svg>
								</div>
								<textarea
									value={importedKey}
									onChange={(e) => setImportedKey(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200 min-h-[120px] resize-none'
									placeholder='Paste your encrypted key data here'
								/>
							</div>
							<p className='text-zinc-500 text-xs'>
								Paste the encrypted key data you exported earlier
							</p>
						</div>
						<div className='space-y-2'>
							<label className='block text-zinc-300 text-sm font-medium mb-2'>
								Password
							</label>
							<div className='relative'>
								<div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5 text-zinc-400'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
										/>
									</svg>
								</div>
								<input
									type='password'
									value={importPassword}
									onChange={(e) => setImportPassword(e.target.value)}
									className='w-full pl-10 pr-4 py-3 bg-zinc-700/60 backdrop-blur-sm text-white rounded-xl border border-zinc-600/30 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-200'
									placeholder='Enter the key password'
								/>
							</div>
							<p className='text-zinc-500 text-xs'>
								Enter the password you used when exporting the key
							</p>
						</div>
						<button
							type='submit'
							disabled={isLoading}
							className='w-full py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-emerald-900/30 font-medium'
						>
							{isLoading ? (
								<div className='flex items-center justify-center gap-2'>
									<div className='w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'></div>
									Importing...
								</div>
							) : (
								<div className='flex items-center justify-center gap-2'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										className='h-5 w-5'
										fill='none'
										viewBox='0 0 24 24'
										stroke='currentColor'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={2}
											d='M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10'
										/>
									</svg>
									Import Key
								</div>
							)}
						</button>
					</form>
				)}

				{/* Export Key Form */}
				{mode === AuthMode.EXPORT_KEY && (
					<div className='space-y-6'>
						{!exportedKey ? (
							<form onSubmit={handleExportKey} className='space-y-6'>
								<div>
									<label className='block text-zinc-300 mb-2'>Password</label>
									<input
										type='password'
										value={exportPassword}
										onChange={(e) => setExportPassword(e.target.value)}
										className='w-full p-3 bg-zinc-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500'
										placeholder='Create a password to protect your key'
									/>
									<p className='text-zinc-400 text-sm mt-1'>
										This password will be required to import your key later.
									</p>
								</div>
								<button
									type='submit'
									disabled={isLoading}
									className='w-full py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed'
								>
									{isLoading ? "Exporting..." : "Export Key"}
								</button>
							</form>
						) : (
							<div>
								<label className='block text-zinc-300 mb-2'>
									Your Encrypted Key
								</label>
								<div className='relative'>
									<textarea
										value={exportedKey}
										readOnly
										className='w-full p-3 bg-zinc-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 min-h-[150px]'
									/>
									<button
										onClick={copyToClipboard}
										className='absolute top-2 right-2 p-2 bg-zinc-600 hover:bg-zinc-500 rounded-md text-zinc-200'
										title='Copy to clipboard'
									>
										<svg
											xmlns='http://www.w3.org/2000/svg'
											className='h-5 w-5'
											fill='none'
											viewBox='0 0 24 24'
											stroke='currentColor'
										>
											<path
												strokeLinecap='round'
												strokeLinejoin='round'
												strokeWidth={2}
												d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3'
											/>
										</svg>
									</button>
								</div>
								<p className='text-zinc-400 text-sm mt-2'>
									Save this key in a secure location. You'll need it and the
									password you set to recover your account.
								</p>
								<button
									onClick={() => setExportedKey("")}
									className='mt-4 w-full py-3 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition'
								>
									Export Another Key
								</button>
							</div>
						)}
					</div>
				)}

				{/* Footer */}
				<div className='mt-8 pt-6 border-t border-zinc-700/50 text-center'>
					<div className='flex items-center justify-center gap-2 mb-2'>
						<div className='w-2 h-2 bg-emerald-500 rounded-full animate-pulse'></div>
						<p className='text-zinc-400 text-sm font-medium'>
							End-to-End Encrypted
						</p>
					</div>
					<p className='text-zinc-500 text-xs leading-relaxed'>
						All messages are protected with military-grade encryption.
						<br />
						Your privacy is our priority.
					</p>
				</div>
			</div>
		</div>
	);
};

export default LoginPage;
