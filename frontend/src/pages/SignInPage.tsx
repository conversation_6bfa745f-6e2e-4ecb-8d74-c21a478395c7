import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import LoadingScreen from "../components/LoadingScreen";
import { useAuth } from "../contexts/AuthContext";
import logger from "../utils/logger";

const SignInPage: React.FC = () => {
	const navigate = useNavigate();
	const { register, loading } = useAuth();
	const [error, setError] = useState<string | null>(null);

	const handleSignIn = async () => {
		try {
			// Generate a random username and password for quick sign-in
			const randomUsername = `user_${Math.random()
				.toString(36)
				.substring(2, 8)}`;
			const randomPassword = Math.random().toString(36).substring(2, 10);

			const userData = await register(randomUsername, randomPassword);
			if (userData) {
				logger.info("Quick registration successful", { userId: userData.id });
				// Wait for next tick to ensure state is updated
				await new Promise((resolve) => setTimeout(resolve, 0));
				navigate("/chat", { replace: true });
			}
		} catch (error) {
			setError("Failed to initialize secure connection");
		}
	};

	useEffect(() => {
		handleSignIn();
	}, []);

	if (loading) {
		return <LoadingScreen message='Initializing secure connection...' />;
	}

	return (
		<div className='flex h-screen items-center justify-center bg-zinc-950'>
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				className='text-center'
			>
				<h1 className='text-2xl font-bold text-emerald-400 mb-8'>
					Secure Chat
				</h1>
				{error && <p className='text-red-500 mb-4'>{error}</p>}
				<button
					onClick={handleSignIn}
					disabled={loading}
					className='px-6 py-3 bg-emerald-500 text-black font-medium rounded-lg
                             hover:bg-emerald-400 transition-colors
                             disabled:opacity-50 disabled:cursor-not-allowed'
				>
					{loading ? "Initializing..." : "Start Secure Chat"}
				</button>
			</motion.div>
		</div>
	);
};

export default SignInPage;
