import { User } from '../types/auth';
import { 
  generateKeyPair, 
  exportPublic<PERSON>ey, 
  import<PERSON>eyPair,
  arrayBufferToBase64,
  base64ToArrayBuffer
} from '../utils/crypto';
import logger from '../utils/logger';

// For now, we'll simulate server-side authentication
// In a real implementation, these would be API calls to a backend
const STORAGE_KEY = 'cypher-chat-auth';

// Helper to simulate server-side storage
const getStoredUsers = (): Record<string, any> => {
  const stored = localStorage.getItem('cypher-chat-users');
  return stored ? JSON.parse(stored) : {};
};

const saveStoredUsers = (users: Record<string, any>) => {
  localStorage.setItem('cypher-chat-users', JSON.stringify(users));
};

export const authService = {
  // Register a new user
  async register(username: string, password: string): Promise<User> {
    logger.info('Registering new user', { username });
    
    // Check if username already exists
    const users = getStoredUsers();
    if (users[username]) {
      throw new Error('Username already exists');
    }
    
    // Generate keys client-side
    const keyPair = await generateKeyPair();
    const exportedPublicKey = await exportPublicKey(keyPair.publicKey);
    
    // Generate a token (in a real app, this would come from the server)
    const token = crypto.randomUUID();
    const id = crypto.randomUUID();
    
    // Store user data (simulating server-side storage)
    users[username] = {
      id,
      username,
      password, // In a real app, this would be hashed
      publicKey: exportedPublicKey,
      token
    };
    saveStoredUsers(users);
    
    // Store auth token in localStorage (in a real app, this would be an HTTP-only cookie)
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      id,
      username,
      token,
      exportedPublicKey
    }));
    
    return {
      id,
      username,
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
      exportedPublicKey,
      token
    };
  },
  
  // Login with username/password
  async login(username: string, password: string): Promise<User> {
    logger.info('Logging in user', { username });
    
    // Check credentials
    const users = getStoredUsers();
    const user = users[username];
    
    if (!user || user.password !== password) {
      throw new Error('Invalid username or password');
    }
    
    // Generate a new token
    const token = crypto.randomUUID();
    users[username].token = token;
    saveStoredUsers(users);
    
    // Import the key pair
    const keyPair = await importKeyPair(user.publicKey);
    
    // Store auth data
    localStorage.setItem(STORAGE_KEY, JSON.stringify({
      id: user.id,
      username,
      token,
      exportedPublicKey: user.publicKey
    }));
    
    return {
      id: user.id,
      username,
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
      exportedPublicKey: user.publicKey,
      token
    };
  },
  
  // Logout
  async logout(): Promise<void> {
    logger.info('Logging out user');
    
    // In a real app, we would call the server to invalidate the token
    
    // Clear local storage
    localStorage.removeItem(STORAGE_KEY);
  },
  
  // Restore session
  async restoreSession(): Promise<User | null> {
    try {
      // Check if we have user data
      const authData = localStorage.getItem(STORAGE_KEY);
      if (!authData) return null;
      
      const { id, username, token, exportedPublicKey } = JSON.parse(authData);
      
      // Verify token (in a real app, this would be a server call)
      const users = getStoredUsers();
      const user = Object.values(users).find((u: any) => u.id === id);
      
      if (!user || user.token !== token) {
        // Token invalid, clear storage
        localStorage.removeItem(STORAGE_KEY);
        return null;
      }
      
      // Import the key pair
      const keyPair = await importKeyPair(exportedPublicKey);
      
      logger.info('Restored user session', { username });
      
      return {
        id,
        username,
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey,
        exportedPublicKey,
        token
      };
    } catch (error) {
      logger.error('Failed to restore session', { error });
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }
  },
  
  // Export key with password protection
  async exportKey(user: User, password: string): Promise<string> {
    logger.info('Exporting key with password protection');
    
    // Generate salt and IV
    const salt = window.crypto.getRandomValues(new Uint8Array(16));
    const iv = window.crypto.getRandomValues(new Uint8Array(12));
    
    // Derive key from password
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(password),
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    );
    
    const encryptionKey = await window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt']
    );
    
    // Encrypt the key data
    const keyData = JSON.stringify({
      id: user.id,
      username: user.username,
      exportedPublicKey: user.exportedPublicKey
    });
    
    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      encryptionKey,
      new TextEncoder().encode(keyData)
    );
    
    // Format the encrypted key
    return JSON.stringify({
      version: 2,
      salt: arrayBufferToBase64(salt),
      iv: arrayBufferToBase64(iv),
      data: arrayBufferToBase64(encryptedData)
    });
  },
  
  // Import key with password
  async importKey(encryptedKeyData: string, password: string): Promise<{ id: string, username: string, exportedPublicKey: string }> {
    logger.info('Importing key with password');
    
    try {
      const keyData = JSON.parse(encryptedKeyData);
      
      // Handle legacy format (version 1 or unversioned)
      if (!keyData.version || keyData.version === 1) {
        // Try to parse as legacy format
        if (keyData.id && keyData.exportedPublicKey) {
          return {
            id: keyData.id,
            username: `user_${keyData.id.substring(0, 8)}`, // Generate a username for legacy keys
            exportedPublicKey: keyData.exportedPublicKey
          };
        } else if (typeof encryptedKeyData === 'string' && encryptedKeyData.includes(':')) {
          // Handle simple format (ID:KEY)
          const [id, exportedPublicKey] = encryptedKeyData.split(':');
          return {
            id,
            username: `user_${id.substring(0, 8)}`,
            exportedPublicKey
          };
        }
        
        throw new Error('Unsupported key format');
      }
      
      if (keyData.version !== 2) {
        throw new Error('Unsupported key format version');
      }
      
      const salt = base64ToArrayBuffer(keyData.salt);
      const iv = base64ToArrayBuffer(keyData.iv);
      const encryptedData = base64ToArrayBuffer(keyData.data);
      
      // Derive key from password
      const keyMaterial = await window.crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        { name: 'PBKDF2' },
        false,
        ['deriveBits', 'deriveKey']
      );
      
      const decryptionKey = await window.crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt,
          iterations: 100000,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: 'AES-GCM', length: 256 },
        false,
        ['decrypt']
      );
      
      // Decrypt the data
      const decryptedData = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv
        },
        decryptionKey,
        encryptedData
      );
      
      const userData = JSON.parse(new TextDecoder().decode(decryptedData));
      
      if (!userData.id || !userData.username || !userData.exportedPublicKey) {
        throw new Error('Invalid key data format');
      }
      
      return userData;
    } catch (error) {
      throw new Error('Failed to import key: ' + (error instanceof Error ? error.message : String(error)));
    }
  },
  
  // Login with imported key
  async loginWithKey(encryptedKeyData: string, password: string): Promise<User> {
    try {
      // Import and decrypt the key
      const { id, username, exportedPublicKey } = await this.importKey(encryptedKeyData, password);
      
      // Check if user exists in our simulated database
      const users = getStoredUsers();
      let user = Object.values(users).find((u: any) => u.id === id);
      
      // If user doesn't exist, create them
      if (!user) {
        // Generate a random password for the imported user
        const randomPassword = crypto.randomUUID();
        
        // Register the user
        return await this.register(username, randomPassword);
      }
      
      // Otherwise, log them in
      const keyPair = await importKeyPair(exportedPublicKey);
      
      // Generate a new token
      const token = crypto.randomUUID();
      users[username].token = token;
      saveStoredUsers(users);
      
      // Store auth data
      localStorage.setItem(STORAGE_KEY, JSON.stringify({
        id,
        username,
        token,
        exportedPublicKey
      }));
      
      return {
        id,
        username,
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey,
        exportedPublicKey,
        token
      };
    } catch (error) {
      throw new Error('Failed to login with key: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
};

export default authService;
