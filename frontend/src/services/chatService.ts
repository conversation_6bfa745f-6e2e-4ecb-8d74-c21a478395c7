import { WebSocketMessage } from '../types/messages';
import { encryptMessage, decryptMessage } from '../utils/crypto';

export class ChatService {
    private ws: WebSocket | null = null;
    private messageHandlers: ((msg: WebSocketMessage) => void)[] = [];

    connect(url: string) {
        this.ws = new WebSocket(url);
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data) as WebSocketMessage;
            this.messageHandlers.forEach(handler => handler(message));
        };
    }

    async sendMessage(recipientId: string, content: string, recipientPublicKey: string) {
        if (!this.ws) return;

        // Encrypt message with recipient's public key
        const encrypted = await encryptMessage(content, recipientPublicKey);

        const message: ChatMessage = {
            type: 'chat',
            content: encrypted,
            recipientId,
            timestamp: new Date().toISOString(),
            encrypted: true
        };

        this.ws.send(JSON.stringify(message));
    }

    onMessage(handler: (msg: WebSocketMessage) => void) {
        this.messageHandlers.push(handler);
    }
} 