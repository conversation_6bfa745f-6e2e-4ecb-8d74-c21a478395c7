/* Enhanced UI Styles for Cypher Chat */

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(39, 39, 42, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(113, 113, 122, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(113, 113, 122, 0.8);
}

/* Smooth animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
  }
}

/* Message animations */
.message-enter {
  animation: fadeInUp 0.3s ease-out;
}

.message-self {
  animation: slideInFromRight 0.3s ease-out;
}

.message-other {
  animation: slideInFromLeft 0.3s ease-out;
}

/* Typing indicator animation */
@keyframes typing-dot {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.typing-dot-1 {
  animation: typing-dot 1.4s infinite;
  animation-delay: 0s;
}

.typing-dot-2 {
  animation: typing-dot 1.4s infinite;
  animation-delay: 0.2s;
}

.typing-dot-3 {
  animation: typing-dot 1.4s infinite;
  animation-delay: 0.4s;
}

/* Enhanced button hover effects */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(39, 39, 42, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(113, 113, 122, 0.2);
}

/* Enhanced focus states */
.focus-enhanced:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5);
  border-color: rgba(16, 185, 129, 0.8);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #ffffff, #d4d4d8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced shadows */
.shadow-enhanced {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.shadow-enhanced-lg {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Loading spinner */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin-slow {
  animation: spin-slow 2s linear infinite;
}

/* Notification slide in */
@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.notification-enter {
  animation: slideInFromBottom 0.3s ease-out;
}

/* Enhanced status indicators */
.status-online {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.4);
}

.status-offline {
  background: linear-gradient(135deg, #71717a, #52525b);
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
  
  .mobile-padding {
    padding: 1rem;
  }
}

/* Dark mode enhancements */
.dark-enhanced {
  background: linear-gradient(135deg, #18181b 0%, #27272a 50%, #18181b 100%);
}

/* Hover effects for interactive elements */
.interactive-hover {
  transition: all 0.2s ease;
}

.interactive-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced border effects */
.border-glow {
  border: 1px solid transparent;
  background: linear-gradient(#27272a, #27272a) padding-box,
              linear-gradient(135deg, #10b981, #059669) border-box;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Enhanced emoji picker */
.emoji-picker {
  background: rgba(39, 39, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(113, 113, 122, 0.3);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced message bubbles */
.message-bubble {
  position: relative;
  backdrop-filter: blur(10px);
}

.message-bubble::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}
