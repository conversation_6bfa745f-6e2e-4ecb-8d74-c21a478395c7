export interface DeliveryInfo {
  messageId: string;
  delivered: boolean;
  readAt?: Date;
}

export interface Message {
  id: string;
  content: string;
  senderId: string;
  recipientId: string;
  timestamp: Date;
  encrypted: boolean;
  deliveryInfo: {
    delivered: boolean;
    readAt?: Date;
  };
}

export interface ChatState {
  messages: Message[];
  selectedClientId: string | null;
  isTyping: boolean;
}

export type MessageType = 
  | 'message'
  | 'client_list'
  | 'delivery_status'
  | 'typing'
  | 'error';

export interface WebSocketMessage {
  type: MessageType;
  content?: any;
  timestamp: string;
}

export interface TypingStatus {
  clientId: string;
  isTyping: boolean;
}

export interface FileTransfer {
  filename: string;
  fileSize: number;
  fileType: string;
  content: ArrayBuffer;
  senderId: string;
  recipientId: string;
}

export interface MessageState {
  messages: Message[];
  loading: boolean;
  error: Error | null;
}
