export interface BaseMessage {
    type: string;
    timestamp?: string;
}

export interface ClientListMessage extends BaseMessage {
    type: 'client_list';
    content: {
        clients: {
            id: string;
            publicKey: string;
            isOnline: boolean;
        }[];
    };
}

export interface ChatMessage extends BaseMessage {
    type: 'chat';
    content: string;
    senderId: string;
    recipientId: string;
    encrypted: boolean;
}

export interface PublicKeyMessage extends BaseMessage {
    type: 'public_key';
    content: string;
}

export type WebSocketMessage = ClientListMessage | ChatMessage | PublicKeyMessage; 