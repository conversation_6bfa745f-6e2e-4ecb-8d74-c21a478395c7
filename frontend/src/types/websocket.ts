export interface WebSocketMessage {
  type: string;
  id: string;
  content: string | Record<string, unknown>;
  timestamp: string;
  sender_id: string;
  recipient_id: string;
  delivery_info?: {
    delivered: boolean;
    read_at?: string;
  };
  error?: string;
}

export interface ClientListUpdate extends WebSocketMessage {
  type: 'client_list';
  clients: {
    id: string;
    public_key: string;
    is_online: boolean;
  }[];
}

export interface DeliveryStatusUpdate extends WebSocketMessage {
  type: 'delivery_status';
  message_id: string;
  delivery_info: {
    delivered: boolean;
    read_at?: string;
  };
}

export interface ErrorMessage extends WebSocketMessage {
  type: 'error';
  error: string;
}

export interface ChatMessage extends WebSocketMessage {
  type: 'message';
  content: string;
  encrypted: boolean;
}

// Add message type constants
export const MessageTypeChat = 'chat';
export const MessageTypePublicKey = 'public_key';
export const MessageTypeClientList = 'client_list';
export const MessageTypeDelivery = 'delivery_status';
export const MessageTypeError = 'error';
