import { Buffer } from "buffer";
import { User } from "../types/auth";

// Generate a new RSA key pair
export async function generateKeyPair(): Promise<CryptoKeyPair> {
	return window.crypto.subtle.generateKey(
		{
			name: "RSA-OAEP",
			modulusLength: 2048,
			publicExponent: new Uint8Array([1, 0, 1]),
			hash: "SHA-256",
		},
		true,
		["encrypt", "decrypt"]
	);
}

// Generate a random AES key
export async function generateAESKey(): Promise<CryptoKey> {
	return await window.crypto.subtle.generateKey(
		{
			name: "AES-GCM",
			length: 256,
		},
		true,
		["encrypt", "decrypt"]
	);
}

// Export public key to PEM format
export async function exportPublicKey(key: CryptoKey): Promise<string> {
	const exported = await window.crypto.subtle.exportKey("spki", key);
	return btoa(String.fromCharCode(...new Uint8Array(exported)));
}

// Import public key from PEM format or raw base64
export async function importPublic<PERSON>ey(key: string): Promise<CryptoKey> {
	let base64Data = key;

	// Check if the key is in PEM format
	if (key.includes("-----BEGIN PUBLIC KEY-----")) {
		const pemHeader = "-----BEGIN PUBLIC KEY-----";
		const pemFooter = "-----END PUBLIC KEY-----";
		base64Data = key
			.substring(
				key.indexOf(pemHeader) + pemHeader.length,
				key.indexOf(pemFooter)
			)
			.replace(/\s/g, "");
	}

	// Convert base64 to binary
	let binaryDer;
	try {
		binaryDer = base64ToArrayBuffer(base64Data);
	} catch (error) {
		console.error("Failed to convert base64 to binary", error);
		throw new Error("Invalid public key format");
	}

	// Import the key
	try {
		return await window.crypto.subtle.importKey(
			"spki",
			binaryDer,
			{
				name: "RSA-OAEP",
				hash: "SHA-256",
			},
			true,
			["encrypt"]
		);
	} catch (error) {
		console.error("Failed to import public key", error);
		throw new Error(
			"Failed to import public key: " +
				(error instanceof Error ? error.message : String(error))
		);
	}
}

// Encrypt a message using hybrid encryption (RSA + AES)
export async function encryptMessage(
	message: string,
	publicKeyPem: string
): Promise<string> {
	try {
		console.log(
			"Encrypting message with public key:",
			publicKeyPem.substring(0, 20) + "..."
		);

		// Import recipient's public key
		const publicKey = await importPublicKey(publicKeyPem);

		// Generate a random AES key
		const aesKey = await generateAESKey();

		// Encrypt the AES key with RSA
		const exportedAesKey = await window.crypto.subtle.exportKey("raw", aesKey);
		const encryptedAesKey = await window.crypto.subtle.encrypt(
			{
				name: "RSA-OAEP",
			},
			publicKey,
			exportedAesKey
		);

		// Generate a random IV
		const iv = window.crypto.getRandomValues(new Uint8Array(12));

		// Encrypt the message with AES
		const encodedMessage = new TextEncoder().encode(message);
		const encryptedMessage = await window.crypto.subtle.encrypt(
			{
				name: "AES-GCM",
				iv: iv,
			},
			aesKey,
			encodedMessage
		);

		// Combine the encrypted AES key, IV, and encrypted message
		const combined = new Uint8Array(
			encryptedAesKey.byteLength + iv.byteLength + encryptedMessage.byteLength
		);
		combined.set(new Uint8Array(encryptedAesKey), 0);
		combined.set(iv, encryptedAesKey.byteLength);
		combined.set(
			new Uint8Array(encryptedMessage),
			encryptedAesKey.byteLength + iv.byteLength
		);

		return Buffer.from(combined).toString("base64");
	} catch (error) {
		console.error("Error encrypting message:", error);
		throw new Error(
			"Failed to encrypt message: " +
				(error instanceof Error ? error.message : String(error))
		);
	}
}

// Decrypt a message using hybrid decryption (RSA + AES)
export async function decryptMessage(
	encryptedMessage: string,
	privateKey: CryptoKey
): Promise<string> {
	try {
		// Convert base64 to binary
		const combined = Buffer.from(encryptedMessage, "base64");

		// Extract the encrypted AES key (first 256 bytes for RSA-2048)
		const encryptedAesKey = combined.slice(0, 256);

		// Extract the IV (next 12 bytes)
		const iv = combined.slice(256, 268);

		// Extract the encrypted message (remaining bytes)
		const encrypted = combined.slice(268);

		console.log("Decrypting message with sizes:", {
			totalLength: combined.length,
			keyLength: encryptedAesKey.length,
			ivLength: iv.length,
			messageLength: encrypted.length,
		});

		// Decrypt the AES key
		const aesKeyBuffer = await window.crypto.subtle.decrypt(
			{
				name: "RSA-OAEP",
			},
			privateKey,
			encryptedAesKey
		);

		// Import the decrypted AES key
		const aesKey = await window.crypto.subtle.importKey(
			"raw",
			aesKeyBuffer,
			{
				name: "AES-GCM",
				length: 256,
			},
			true,
			["decrypt"]
		);

		// Decrypt the message
		const decrypted = await window.crypto.subtle.decrypt(
			{
				name: "AES-GCM",
				iv: iv,
			},
			aesKey,
			encrypted
		);

		return new TextDecoder().decode(decrypted);
	} catch (error) {
		console.error("Error decrypting message:", error);
		throw new Error(
			"Failed to decrypt message: " +
				(error instanceof Error ? error.message : String(error))
		);
	}
}

// Generate a random client ID
export function generateClientId(): string {
	const array = new Uint8Array(16);
	window.crypto.getRandomValues(array);
	return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
		""
	);
}

// Utility functions for base64 conversion
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
	try {
		// Remove any whitespace that might be in the base64 string
		base64 = base64.replace(/\s/g, "");

		// Try to decode the base64 string
		const binaryString = atob(base64);
		const bytes = new Uint8Array(binaryString.length);
		for (let i = 0; i < binaryString.length; i++) {
			bytes[i] = binaryString.charCodeAt(i);
		}
		return bytes.buffer;
	} catch (error) {
		console.error("Error converting base64 to ArrayBuffer:", error);
		// Try using Buffer as a fallback
		try {
			const buffer = Buffer.from(base64, "base64");
			return buffer.buffer.slice(
				buffer.byteOffset,
				buffer.byteOffset + buffer.byteLength
			);
		} catch (bufferError) {
			console.error("Buffer fallback also failed:", bufferError);
			throw new Error("Failed to convert base64 to binary data");
		}
	}
}

// Update the importKeyPair function
export const importKeyPair = async (
	exportedPublicKey: string
): Promise<CryptoKeyPair> => {
	try {
		// Validate base64 format
		if (!exportedPublicKey || !/^[A-Za-z0-9+/=]+$/.test(exportedPublicKey)) {
			throw new Error(
				"Invalid public key format: Key must be in base64 format"
			);
		}

		const publicKeyBuffer = base64ToArrayBuffer(exportedPublicKey);

		// Generate a new key pair for the client
		const keyPair = await window.crypto.subtle.generateKey(
			{
				name: "RSA-OAEP",
				modulusLength: 2048, // Match the key size used in generation
				publicExponent: new Uint8Array([1, 0, 1]),
				hash: "SHA-256",
			},
			true,
			["encrypt", "decrypt"]
		);

		// Import the provided public key
		const publicKey = await window.crypto.subtle.importKey(
			"spki",
			publicKeyBuffer,
			{
				name: "RSA-OAEP",
				hash: "SHA-256",
			},
			true,
			["encrypt"]
		);

		return {
			publicKey,
			privateKey: keyPair.privateKey,
		};
	} catch (error) {
		console.error("Key import failed:", error);
		if (error instanceof Error) {
			throw new Error(`Failed to import key: ${error.message}`);
		}
		throw new Error("Failed to import key - unknown error");
	}
};

// Add a function to export the full key data
export const exportKeyData = async (user: User): Promise<string> => {
	return JSON.stringify({
		id: user.id,
		exportedPublicKey: user.exportedPublicKey,
	});
};

export const formatKeyData = (
	id: string,
	exportedPublicKey: string
): string => {
	try {
		// Create a simple format: ID:KEY
		return `${id}:${exportedPublicKey}`;
	} catch (error) {
		throw new Error("Failed to format key data");
	}
};

// Keep the JSON format function for backward compatibility
export const formatKeyDataAsJson = (
	id: string,
	exportedPublicKey: string
): string => {
	try {
		const keyData = {
			version: 1,
			id,
			exportedPublicKey,
		};
		return JSON.stringify(keyData, null, 2); // Pretty print for better readability
	} catch (error) {
		throw new Error("Failed to format key data");
	}
};

export const validateKeyData = (
	keyData: string
): { id: string; exportedPublicKey: string } => {
	if (!keyData) {
		throw new Error("Key data is empty");
	}

	// First, try the simple format (ID:KEY)
	if (keyData.includes(":")) {
		try {
			const [id, exportedPublicKey] = keyData.split(":");

			if (!id || typeof id !== "string") {
				throw new Error("Invalid or missing user ID");
			}

			if (!exportedPublicKey || typeof exportedPublicKey !== "string") {
				throw new Error("Invalid or missing public key");
			}

			// Validate base64 format of the public key
			if (!/^[A-Za-z0-9+/=]+$/.test(exportedPublicKey)) {
				throw new Error("Public key must be in base64 format");
			}

			return { id, exportedPublicKey };
		} catch (error) {
			if (error instanceof Error) {
				throw new Error(`Invalid key format: ${error.message}`);
			}
			throw new Error("Invalid key format");
		}
	}

	// If not simple format, try JSON format (for backward compatibility)
	try {
		const parsed = JSON.parse(keyData);

		if (!parsed.id || typeof parsed.id !== "string") {
			throw new Error("Invalid or missing user ID in JSON");
		}

		if (
			!parsed.exportedPublicKey ||
			typeof parsed.exportedPublicKey !== "string"
		) {
			throw new Error("Invalid or missing public key in JSON");
		}

		// Validate base64 format of the public key
		if (!/^[A-Za-z0-9+/=]+$/.test(parsed.exportedPublicKey)) {
			throw new Error("Public key must be in base64 format");
		}

		return {
			id: parsed.id,
			exportedPublicKey: parsed.exportedPublicKey,
		};
	} catch (error) {
		if (error instanceof Error) {
			throw new Error(`Invalid key data: ${error.message}`);
		}
		throw new Error("Invalid key data format");
	}
};

// Utility functions for base64 conversion are defined elsewhere in this file

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
	const bytes = new Uint8Array(buffer);
	let binary = "";
	for (let i = 0; i < bytes.byteLength; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return btoa(binary);
}

// Import key pair from exported public key is defined elsewhere in this file
