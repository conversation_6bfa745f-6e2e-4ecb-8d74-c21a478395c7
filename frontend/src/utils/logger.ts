type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogMessage {
    level: LogLevel;
    message: string;
    data?: any;
    timestamp: string;
}

const logger = {
    debug: (message: string, data?: any) => log('debug', message, data),
    info: (message: string, data?: any) => log('info', message, data),
    warn: (message: string, data?: any) => log('warn', message, data),
    error: (message: string, data?: any) => log('error', message, data),
};

function log(level: LogLevel, message: string, data?: any) {
    const logMessage: LogMessage = {
        level,
        message,
        data,
        timestamp: new Date().toISOString(),
    };

    if (process.env.NODE_ENV === 'development') {
        console[level](message, data);
    }

    // Could add remote logging here
}

export default logger;
