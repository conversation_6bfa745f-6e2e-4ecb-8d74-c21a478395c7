// Sound utility for playing notification sounds

// Create audio elements for different notification sounds
const messageSound = new Audio('/sounds/message.mp3');
const typingSound = new Audio('/sounds/typing.mp3');

// Configure audio elements
messageSound.volume = 0.5;
typingSound.volume = 0.3;

// Flag to track if sounds are enabled
let soundsEnabled = true;

/**
 * Play a notification sound for a new message
 */
export const playMessageSound = () => {
  if (soundsEnabled && document.visibilityState !== 'visible') {
    messageSound.play().catch(error => {
      console.error('Failed to play message sound:', error);
    });
  }
};

/**
 * Play a subtle sound when someone starts typing
 */
export const playTypingSound = () => {
  if (soundsEnabled && document.visibilityState === 'visible') {
    typingSound.play().catch(error => {
      // Silently fail - typing sound is non-critical
    });
  }
};

/**
 * Enable or disable notification sounds
 */
export const setSoundsEnabled = (enabled: boolean) => {
  soundsEnabled = enabled;
};

/**
 * Check if sounds are currently enabled
 */
export const areSoundsEnabled = () => soundsEnabled;
