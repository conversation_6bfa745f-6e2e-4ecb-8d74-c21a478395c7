/** @type {import('tailwindcss').Config} */
export default {
	content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			colors: {
				// New color palette
				"light-sage-green": "#96ceb4",
				"cerulean-blue": "#1398b9",
				"ivory-cream": "#fcfaee",
				"chartreuse-neon": "#dfff00",
				"minty-aqua": "#31ffb6",
				"baby-blue": "#98e4ff",
				"crimson-red": "#ff204e",

				// Semantic color mappings
				primary: "#1398b9", // cerulean-blue
				secondary: "#96ceb4", // light-sage-green
				accent: "#31ffb6", // minty-aqua
				background: "#fcfaee", // ivory-cream
				surface: "#98e4ff", // baby-blue
				warning: "#dfff00", // chartreuse-neon
				error: "#ff204e", // crimson-red

				// Keep some terminal colors for compatibility
				"terminal-bg": "#0a0a0a",
				"terminal-text": "#00ff00",
				"terminal-accent": "#00cc00",
				"terminal-accent-hover": "#00aa00",
				"terminal-border": "#1a1a1a",
				"terminal-error": "#ff0000",
			},
			animation: {
				fadeIn: "fadeIn 0.2s ease-in-out",
				slideIn: "slideIn 0.3s ease-out",
				pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0" },
					"100%": { opacity: "1" },
				},
				slideIn: {
					"0%": { transform: "translateY(10px)", opacity: "0" },
					"100%": { transform: "translateY(0)", opacity: "1" },
				},
				pulse: {
					"0%, 100%": { opacity: "1" },
					"50%": { opacity: "0.5" },
				},
			},
		},
	},
	plugins: [],
};
