/** @type {import('tailwindcss').Config} */
export default {
	content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			colors: {
				"terminal-bg": "#0a0a0a",
				"terminal-text": "#00ff00",
				"terminal-accent": "#00cc00",
				"terminal-accent-hover": "#00aa00",
				"terminal-border": "#1a1a1a",
				"terminal-error": "#ff0000",
			},
			animation: {
				fadeIn: "fadeIn 0.2s ease-in-out",
				slideIn: "slideIn 0.3s ease-out",
				pulse: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
			},
			keyframes: {
				fadeIn: {
					"0%": { opacity: "0" },
					"100%": { opacity: "1" },
				},
				slideIn: {
					"0%": { transform: "translateY(10px)", opacity: "0" },
					"100%": { transform: "translateY(0)", opacity: "1" },
				},
				pulse: {
					"0%, 100%": { opacity: "1" },
					"50%": { opacity: "0.5" },
				},
			},
		},
	},
	plugins: [],
};
