import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

// https://vite.dev/config/
export default defineConfig({
	plugins: [react()],
	build: {
		sourcemap: true,
		rollupOptions: {
			output: {
				sourcemapExcludeSources: false,
			},
		},
	},
	server: {
		host: "localhost",
		port: 3000, // Change to a different port
		strictPort: true,
		hmr: {
			protocol: "ws",
			host: "localhost",
			port: 3001, // Use different port for HMR to avoid conflicts
			clientPort: 3001,
			timeout: 5000,
		},
		fs: {
			strict: false,
			allow: [".."],
		},
		watch: {
			usePolling: true,
		},
		proxy: {
			"/ws": {
				target: "ws://localhost:8080",
				ws: true,
				changeOrigin: true,
				configure: (proxy) => {
					proxy.on("proxyReq", () => {
						console.log("Proxying WebSocket request to backend");
					});
					proxy.on("close", () => {
						console.log("WebSocket proxy connection closed");
					});
				},
			},
		},
	},
	resolve: {
		alias: {
			buffer: "buffer/",
		},
		extensions: [".tsx", ".ts", ".js"],
	},
	define: {
		"process.env": {},
		global: {},
	},
	optimizeDeps: {
		esbuildOptions: {
			define: {
				global: "globalThis",
			},
		},
	},
});
