#!/bin/bash

# API Endpoint Testing Script for Cypher Chat
# This script tests all available API endpoints

BASE_URL="http://localhost:8080"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to make HTTP requests and show response
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    
    echo -e "\n${YELLOW}=== Testing: $method $endpoint ===${NC}"
    
    if [ -n "$data" ] && [ -n "$headers" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X "$method" \
            -H "$CONTENT_TYPE" \
            -H "$headers" \
            -d "$data" \
            "$BASE_URL$endpoint")
    elif [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X "$method" \
            -H "$CONTENT_TYPE" \
            -d "$data" \
            "$BASE_URL$endpoint")
    elif [ -n "$headers" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X "$method" \
            -H "$headers" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X "$method" \
            "$BASE_URL$endpoint")
    fi
    
    # Extract HTTP status and body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "Status Code: $http_status"
    echo "Response Body:"
    echo "$body" | jq . 2>/dev/null || echo "$body"
    
    # Check if request was successful
    if [[ $http_status -ge 200 && $http_status -lt 300 ]]; then
        print_success "Request successful"
        return 0
    else
        print_error "Request failed"
        return 1
    fi
}

# Variables to store tokens and user data
ACCESS_TOKEN=""
USER_ID=""
USERNAME="testuser_$(date +%s)"
PASSWORD="testpassword123"
PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef\n-----END PUBLIC KEY-----"

print_status "Starting API endpoint tests..."
print_status "Base URL: $BASE_URL"
print_status "Test Username: $USERNAME"

# Test 1: Health Check
print_status "Testing health check endpoint..."
make_request "GET" "/health"

# Test 2: User Registration
print_status "Testing user registration..."
register_data="{
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\",
    \"public_key\": \"$PUBLIC_KEY\"
}"

if make_request "POST" "/api/auth/register" "$register_data"; then
    # Extract token from response
    ACCESS_TOKEN=$(echo "$body" | jq -r '.token' 2>/dev/null)
    USER_ID=$(echo "$body" | jq -r '.user.id' 2>/dev/null)
    print_success "Registration successful. Token: ${ACCESS_TOKEN:0:20}..."
else
    print_error "Registration failed"
fi

# Test 3: User Login (with same credentials)
print_status "Testing user login..."
login_data="{
    \"username\": \"$USERNAME\",
    \"password\": \"$PASSWORD\"
}"

make_request "POST" "/api/auth/login" "$login_data"

# Test 4: Token Verification
if [ -n "$ACCESS_TOKEN" ]; then
    print_status "Testing token verification..."
    make_request "GET" "/api/auth/verify" "" "Authorization: Bearer $ACCESS_TOKEN"
else
    print_warning "Skipping token verification - no access token available"
fi

# Test 5: Token Refresh
if [ -n "$ACCESS_TOKEN" ]; then
    print_status "Testing token refresh..."
    make_request "POST" "/api/auth/refresh" "" "Authorization: Bearer $ACCESS_TOKEN"
else
    print_warning "Skipping token refresh - no access token available"
fi

# Test 6: Logout
if [ -n "$ACCESS_TOKEN" ]; then
    print_status "Testing user logout..."
    make_request "POST" "/api/auth/logout" "" "Authorization: Bearer $ACCESS_TOKEN"
else
    print_warning "Skipping logout - no access token available"
fi

# Test 7: WebSocket Test Endpoint
print_status "Testing WebSocket test endpoint..."
make_request "GET" "/test"

# Test 8: Invalid Endpoints (Error Testing)
print_status "Testing invalid endpoint (should return 404)..."
make_request "GET" "/api/invalid"

print_status "Testing unauthorized access to protected endpoint..."
make_request "POST" "/api/auth/refresh"

# Test 9: CORS Preflight Request
print_status "Testing CORS preflight request..."
curl -s -w "\nHTTP_STATUS:%{http_code}" -X OPTIONS \
    -H "Origin: http://localhost:3000" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type,Authorization" \
    "$BASE_URL/api/auth/login"

echo -e "\n${GREEN}=== API Endpoint Testing Complete ===${NC}"
print_status "All tests have been executed. Check the responses above for any issues."
