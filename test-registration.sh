#!/bin/bash

echo "🧪 Testing Cypher Chat Registration API"
echo "========================================"

# Test health endpoint
echo "1. Testing health endpoint..."
health_response=$(curl -s http://localhost:8080/health 2>/dev/null)
if [ $? -eq 0 ] && [ -n "$health_response" ]; then
    echo "✅ Health check: $health_response"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test registration
echo ""
echo "2. Testing user registration..."
username="testuser_$(date +%s)"
registration_response=$(curl -s -X POST http://localhost:8080/api/auth/register \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$username\",\"password\":\"testpass123\",\"public_key\":\"-----BEGIN PUBLIC KEY-----\\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef\\n-----<PERSON><PERSON> PUBLIC KEY-----\"}" \
    2>/dev/null)

if [ $? -eq 0 ] && echo "$registration_response" | grep -q "token"; then
    echo "✅ Registration successful!"
    echo "Response: $registration_response"
else
    echo "❌ Registration failed"
    echo "Response: $registration_response"
fi

echo ""
echo "🎉 API testing complete!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend:  http://localhost:8080"
echo "🗄️  Database: PostgreSQL running in Docker"
